{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\OperationalEfficiency.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport ApexCharts from 'apexcharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OperationalEfficiencyDashboard = ({\n  headerTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  contentSettings = {},\n  operationalData = null\n}) => {\n  _s();\n  const salesOutstandingRef = useRef(null);\n  const payablesOutstandingRef = useRef(null);\n  const inventoryOutstandingRef = useRef(null);\n  const cashConversionRef = useRef(null);\n  const fixedAssetTurnoverRef = useRef(null);\n\n  // Function to check if a chart should be displayed based on content settings\n  const shouldDisplayChart = chartKey => {\n    if (!(contentSettings !== null && contentSettings !== void 0 && contentSettings.chartSettings)) return true; // Default to true if no settings\n    return contentSettings.chartSettings[chartKey] === true;\n  };\n\n  // Enhanced data validation function - more lenient approach like FiscalYear\n  const isDataLoaded = () => {\n    if (!operationalData) {\n      return false;\n    }\n\n    // Check if at least one of the required data arrays exists and has content\n    const hasSalesOutstandingData = operationalData.daysSalesAROutstanding && Array.isArray(operationalData.daysSalesAROutstanding) && operationalData.daysSalesAROutstanding.length > 0;\n    const hasPayablesOutstandingData = operationalData.daysSalesAPOutstanding && Array.isArray(operationalData.daysSalesAPOutstanding) && operationalData.daysSalesAPOutstanding.length > 0;\n    const hasInventoryOutstandingData = operationalData.daysInventoryOutstanding && Array.isArray(operationalData.daysInventoryOutstanding) && operationalData.daysInventoryOutstanding.length > 0;\n    const hasCashConversionData = operationalData.cashConversionCycle && Array.isArray(operationalData.cashConversionCycle) && operationalData.cashConversionCycle.length > 0;\n    const hasFixedAssetTurnoverData = operationalData.fixedAssetTurnover && Array.isArray(operationalData.fixedAssetTurnover) && operationalData.fixedAssetTurnover.length > 0;\n    return hasSalesOutstandingData || hasPayablesOutstandingData || hasInventoryOutstandingData || hasCashConversionData || hasFixedAssetTurnoverData;\n  };\n\n  // Function to check if we have any meaningful data (not all zeros) - like FiscalYear\n\n  useEffect(() => {\n    if (isDataLoaded()) {\n      // Clear charts first\n      [salesOutstandingRef, payablesOutstandingRef, inventoryOutstandingRef, cashConversionRef, fixedAssetTurnoverRef].forEach(ref => {\n        if (ref.current) {\n          ref.current.innerHTML = \"\";\n        }\n      });\n      // Initialize charts with new data\n      initializeCharts();\n    }\n  }, [operationalData, contentSettings]);\n  const formatMonthYear = (year, month) => {\n    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\n  };\n  const initializeCharts = () => {\n    var _dataToUse$daysSalesA, _dataToUse$daysSalesA2, _dataToUse$daysSalesA3, _dataToUse$daysInvent, _dataToUse$cashConver, _dataToUse$fixedAsset;\n    if (!operationalData) return;\n\n    // Try to find the correct data structure\n    let dataToUse = operationalData;\n\n    // Check if data is nested under different possible paths\n    if (operationalData.operationalEfficiency) {\n      dataToUse = operationalData.operationalEfficiency;\n    } else if (operationalData.operational) {\n      dataToUse = operationalData.operational;\n    } else if (operationalData.data) {\n      dataToUse = operationalData.data;\n    } else if (operationalData.reportData) {\n      dataToUse = operationalData.reportData;\n    }\n\n    // Prepare data from API response - using correct property names from your JSON\n    const categories = ((_dataToUse$daysSalesA = dataToUse.daysSalesAROutstanding) === null || _dataToUse$daysSalesA === void 0 ? void 0 : _dataToUse$daysSalesA.map(item => formatMonthYear(item.year, item.month))) || [];\n    const daysSalesOutstandingData = ((_dataToUse$daysSalesA2 = dataToUse.daysSalesAROutstanding) === null || _dataToUse$daysSalesA2 === void 0 ? void 0 : _dataToUse$daysSalesA2.map(item => {\n      const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    const daysPayablesOutstandingData = ((_dataToUse$daysSalesA3 = dataToUse.daysSalesAPOutstanding) === null || _dataToUse$daysSalesA3 === void 0 ? void 0 : _dataToUse$daysSalesA3.map(item => {\n      const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    const daysInventoryOutstandingData = ((_dataToUse$daysInvent = dataToUse.daysInventoryOutstanding) === null || _dataToUse$daysInvent === void 0 ? void 0 : _dataToUse$daysInvent.map(item => {\n      const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    const cashConversionCycleData = ((_dataToUse$cashConver = dataToUse.cashConversionCycle) === null || _dataToUse$cashConver === void 0 ? void 0 : _dataToUse$cashConver.map(item => {\n      const value = parseFloat(item.CCC || item.ccc);\n      return isNaN(value) || value === null ? 0 : value;\n    })) || [];\n    const fixedAssetTurnoverData = ((_dataToUse$fixedAsset = dataToUse.fixedAssetTurnover) === null || _dataToUse$fixedAsset === void 0 ? void 0 : _dataToUse$fixedAsset.map(item => parseFloat(item.fat) || 0)) || [];\n\n    // Color scheme\n    const colors = {\n      salesOutstanding: '#2d6a9b',\n      payablesOutstanding: '#565aa4',\n      inventoryOutstanding: '#2a689a',\n      cashConversion: '#ff6b47',\n      fixedAssetTurnover: '#2d6a9b'\n    };\n\n    // 1. Days Sales (A/R) Outstanding Chart\n    const salesOutstandingOptions = {\n      series: [{\n        name: 'Days Sales Outstanding',\n        data: daysSalesOutstandingData\n      }],\n      chart: {\n        type: 'area',\n        height: 200,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return Math.round(val);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: [colors.salesOutstanding]\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: colors.salesOutstanding,\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: colors.salesOutstanding,\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: [colors.salesOutstanding],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.salesOutstanding],\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return Math.round(val) + ' days';\n          }\n        }\n      }\n    };\n\n    // 2. Days Payables (AP) Outstanding Chart\n    const payablesOutstandingOptions = {\n      series: [{\n        name: 'Days Payables Outstanding',\n        data: daysPayablesOutstandingData\n      }],\n      chart: {\n        type: 'area',\n        height: 200,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        parentHeightOffset: 0,\n        sparkline: {\n          enabled: false\n        },\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return Math.round(val);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: [colors.payablesOutstanding]\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: colors.payablesOutstanding,\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: colors.payablesOutstanding,\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: [colors.payablesOutstanding],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.payablesOutstanding],\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return Math.round(val) + ' days';\n          }\n        }\n      }\n    };\n\n    // 3. Days Inventory Outstanding Chart (Bar Chart)\n    const inventoryOutstandingOptions = {\n      series: [{\n        name: 'Days Inventory Outstanding',\n        data: daysInventoryOutstandingData\n      }],\n      chart: {\n        type: 'bar',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent'\n      },\n      plotOptions: {\n        bar: {\n          columnWidth: '40%',\n          dataLabels: {\n            position: 'top'\n          }\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return Math.round(val);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -20,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.inventoryOutstanding],\n      grid: {\n        show: false,\n        padding: {\n          left: 10,\n          right: 10,\n          top: 25,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return Math.round(val) + ' days';\n          }\n        }\n      }\n    };\n\n    // 4. Cash Conversion Cycle Chart\n    const cashConversionOptions = {\n      series: [{\n        name: 'Cash Conversion Cycle',\n        data: cashConversionCycleData\n      }],\n      chart: {\n        type: 'line',\n        height: 200,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return Math.round(val);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        width: 2,\n        colors: [colors.cashConversion]\n      },\n      markers: {\n        size: 4,\n        colors: [colors.cashConversion],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.cashConversion],\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return Math.round(val) + ' days';\n          }\n        }\n      }\n    };\n\n    // 5. Fixed Asset Turnover Chart\n    const fixedAssetTurnoverOptions = {\n      series: [{\n        name: 'Fixed Asset Turnover',\n        data: fixedAssetTurnoverData\n      }],\n      chart: {\n        type: 'area',\n        height: 200,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return val.toFixed(2);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: [colors.fixedAssetTurnover]\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: colors.fixedAssetTurnover,\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: colors.fixedAssetTurnover,\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: [colors.fixedAssetTurnover],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.fixedAssetTurnover],\n      grid: {\n        show: false,\n        padding: {\n          left: 15,\n          right: 15,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return val.toFixed(2);\n          }\n        }\n      }\n    };\n\n    // Clear existing charts before rendering new ones\n    const clearAndRenderChart = (ref, options, chartName) => {\n      if (ref.current) {\n        // Clear any existing chart\n        ref.current.innerHTML = '';\n\n        // Wait a tick before rendering to ensure DOM is cleared\n        setTimeout(() => {\n          if (ref.current) {\n            try {\n              const chart = new ApexCharts(ref.current, options);\n              chart.render();\n\n              // Store chart instances globally for export\n              if (chartName === \"Days Sales Outstanding\") {\n                window.salesOutstandingChart = chart;\n              } else if (chartName === \"Days Payables Outstanding\") {\n                window.payablesOutstandingChart = chart;\n              } else if (chartName === \"Days Inventory Outstanding\") {\n                window.inventoryOutstandingChart = chart;\n              } else if (chartName === \"Cash Conversion Cycle\") {\n                window.cashConversionChart = chart;\n              } else if (chartName === \"Fixed Asset Turnover\") {\n                window.fixedAssetTurnoverChart = chart;\n              }\n            } catch (error) {\n              console.error(`OperationalEfficiency - Error rendering ${chartName} chart:`, error);\n              // Show error message in chart container\n              ref.current.innerHTML = `<div class=\"flex items-center justify-center h-48 text-gray-500\">Error loading ${chartName} chart</div>`;\n            }\n          }\n        }, 10);\n      }\n    };\n\n    // Get enabled charts and assign chart options\n    const enabledCharts = getEnabledCharts();\n\n    // Assign chart options to enabled charts\n    enabledCharts.forEach(chart => {\n      switch (chart.key) {\n        case 'daysSalesOutstanding':\n          chart.options = salesOutstandingOptions;\n          chart.name = 'Days Sales Outstanding';\n          break;\n        case 'daysPayablesOutstanding':\n          chart.options = payablesOutstandingOptions;\n          chart.name = 'Days Payables Outstanding';\n          break;\n        case 'daysInventoryOutstanding':\n          chart.options = inventoryOutstandingOptions;\n          chart.name = 'Days Inventory Outstanding';\n          break;\n        case 'cashConversionCycle':\n          chart.options = cashConversionOptions;\n          chart.name = 'Cash Conversion Cycle';\n          break;\n        case 'fixedAssetTurnover':\n          chart.options = fixedAssetTurnoverOptions;\n          chart.name = 'Fixed Asset Turnover';\n          break;\n      }\n    });\n\n    // Render the enabled charts with error handling\n    enabledCharts.forEach(({\n      ref,\n      options,\n      name\n    }) => {\n      if (ref.current) {\n        clearAndRenderChart(ref, options, name);\n      }\n    });\n\n    // Clear all chart containers that are not being used\n    [salesOutstandingRef, payablesOutstandingRef, inventoryOutstandingRef, cashConversionRef, fixedAssetTurnoverRef].forEach(ref => {\n      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {\n        ref.current.innerHTML = \"\";\n      }\n    });\n  };\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  const formatHeaderStyle = () => {\n    const style = {\n      ...headerTextStyle\n    };\n    if (style.fontSize) {\n      const fontSize = parseInt(style.fontSize);\n      style.fontSize = `${fontSize / 2}px`;\n    }\n    return style;\n  };\n  const formatCompanyName = companyName => {\n    if (!companyName) return '';\n    if (companyName.length > 15) {\n      return companyName.substring(0, 15) + '...';\n    }\n    return companyName;\n  };\n\n  // Function to determine which charts should be rendered and their order\n  const getEnabledCharts = () => {\n    // Try to find the correct data structure\n    let dataToUse = operationalData;\n    if (operationalData !== null && operationalData !== void 0 && operationalData.operationalEfficiency) {\n      dataToUse = operationalData.operationalEfficiency;\n    } else if (operationalData !== null && operationalData !== void 0 && operationalData.operational) {\n      dataToUse = operationalData.operational;\n    } else if (operationalData !== null && operationalData !== void 0 && operationalData.data) {\n      dataToUse = operationalData.data;\n    } else if (operationalData !== null && operationalData !== void 0 && operationalData.reportData) {\n      dataToUse = operationalData.reportData;\n    }\n    const allCharts = [{\n      key: 'daysSalesOutstanding',\n      title: 'Days Sales (A/R) Outstanding',\n      ref: salesOutstandingRef,\n      options: null,\n      // Will be set in initializeCharts\n      hasData: () => {\n        var _dataToUse, _dataToUse$daysSalesA4;\n        const daysSalesOutstandingData = ((_dataToUse = dataToUse) === null || _dataToUse === void 0 ? void 0 : (_dataToUse$daysSalesA4 = _dataToUse.daysSalesAROutstanding) === null || _dataToUse$daysSalesA4 === void 0 ? void 0 : _dataToUse$daysSalesA4.map(item => {\n          const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;\n          return parseFloat(value) || 0;\n        })) || [];\n        return daysSalesOutstandingData.length > 0 && daysSalesOutstandingData.some(val => val > 0);\n      }\n    }, {\n      key: 'daysPayablesOutstanding',\n      title: 'Days Payables (AP) Outstanding',\n      ref: payablesOutstandingRef,\n      options: null,\n      hasData: () => {\n        var _dataToUse2, _dataToUse2$daysSales;\n        const daysPayablesOutstandingData = ((_dataToUse2 = dataToUse) === null || _dataToUse2 === void 0 ? void 0 : (_dataToUse2$daysSales = _dataToUse2.daysSalesAPOutstanding) === null || _dataToUse2$daysSales === void 0 ? void 0 : _dataToUse2$daysSales.map(item => {\n          const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;\n          return parseFloat(value) || 0;\n        })) || [];\n        return daysPayablesOutstandingData.length > 0 && daysPayablesOutstandingData.some(val => val > 0);\n      },\n      hasDescription: true,\n      description: {\n        title: 'Days AR Outstanding & Days AP Outstanding',\n        content: 'Average number of days it takes customers to pay for invoices/ average number of days it takes a company to pay its suppliers.'\n      }\n    }, {\n      key: 'daysInventoryOutstanding',\n      title: 'Days Inventory Outstanding',\n      ref: inventoryOutstandingRef,\n      options: null,\n      hasData: () => {\n        var _dataToUse3, _dataToUse3$daysInven;\n        const daysInventoryOutstandingData = ((_dataToUse3 = dataToUse) === null || _dataToUse3 === void 0 ? void 0 : (_dataToUse3$daysInven = _dataToUse3.daysInventoryOutstanding) === null || _dataToUse3$daysInven === void 0 ? void 0 : _dataToUse3$daysInven.map(item => {\n          const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;\n          return parseFloat(value) || 0;\n        })) || [];\n        return daysInventoryOutstandingData.length > 0 && daysInventoryOutstandingData.some(val => val > 0);\n      }\n      // containerMarginBottom: 'mb-24'\n    }, {\n      key: 'cashConversionCycle',\n      title: 'Cash Conversion Cycle',\n      ref: cashConversionRef,\n      options: null,\n      hasData: () => {\n        var _dataToUse4, _dataToUse4$cashConve;\n        const cashConversionCycleData = ((_dataToUse4 = dataToUse) === null || _dataToUse4 === void 0 ? void 0 : (_dataToUse4$cashConve = _dataToUse4.cashConversionCycle) === null || _dataToUse4$cashConve === void 0 ? void 0 : _dataToUse4$cashConve.map(item => {\n          const value = parseFloat(item.CCC || item.ccc);\n          return isNaN(value) || value === null ? 0 : value;\n        })) || [];\n        return cashConversionCycleData.length > 0 && cashConversionCycleData.some(val => val != null && val !== 0);\n      },\n      hasDescription: true,\n      description: {\n        title: 'Cash Conversion Cycle (CCC)',\n        content: 'The time it takes a company to convert the money spent on inventory or production back into cash by selling its goods or services. A shorter CCC is better because it means less time that money is tied up in inventory or accounts receivable.'\n      }\n    }, {\n      key: 'fixedAssetTurnover',\n      title: 'Fixed Asset Turnover',\n      ref: fixedAssetTurnoverRef,\n      options: null,\n      hasData: () => {\n        var _dataToUse5, _dataToUse5$fixedAsse;\n        const fixedAssetTurnoverData = ((_dataToUse5 = dataToUse) === null || _dataToUse5 === void 0 ? void 0 : (_dataToUse5$fixedAsse = _dataToUse5.fixedAssetTurnover) === null || _dataToUse5$fixedAsse === void 0 ? void 0 : _dataToUse5$fixedAsse.map(item => parseFloat(item.fat) || 0)) || [];\n        return fixedAssetTurnoverData.length > 0 && fixedAssetTurnoverData.some(val => val > 0);\n      },\n      hasDescription: true,\n      description: {\n        title: 'Fixed Asset Turnover (FAT)',\n        content: 'The ratio of a company\\'s net sales to its average fixed assets over a specific period, usually a year. A higher ratio indicates that a company is using its fixed assets more efficiently, while a lower ratio suggests underutilization.'\n      }\n    }];\n\n    // Filter charts based on settings and data availability\n    return allCharts.filter(chart => shouldDisplayChart(chart.key) && chart.hasData());\n  };\n\n  // Get enabled charts for dynamic layout\n  const enabledCharts = getEnabledCharts();\n\n  // Split charts between upper and lower divs\n  // Upper div can hold up to 3 charts, lower div gets the rest\n  const upperDivCharts = enabledCharts.slice(0, 3);\n  const lowerDivCharts = enabledCharts.slice(3);\n\n  // Helper function to render a chart component\n  const renderChart = chart => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-white p-6 border-b-4 border-blue-900 ${chart.containerMarginBottom || ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-2xl font-semibold text-teal-600 mb-5\",\n      style: subHeadingTextStyle,\n      children: chart.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 801,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: chart.ref,\n      className: \"mb-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 807,\n      columnNumber: 7\n    }, this), chart.hasDescription && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-teal-600 text-2xl mb-2\",\n        style: {\n          ...subHeadingTextStyle,\n          fontWeight: 'lighter'\n        },\n        children: chart.description.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: contentTextStyle,\n        children: chart.description.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 9\n    }, this)]\n  }, chart.key, true, {\n    fileName: _jsxFileName,\n    lineNumber: 800,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-8 p-10 mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4  border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Operational Efficiency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(operationalData === null || operationalData === void 0 ? void 0 : operationalData.FYStartYear, operationalData === null || operationalData === void 0 ? void 0 : operationalData.FYStartMonth), \" | \", formatCompanyName(operationalData === null || operationalData === void 0 ? void 0 : operationalData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 835,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 828,\n        columnNumber: 9\n      }, this), upperDivCharts.map(chart => renderChart(chart))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 826,\n      columnNumber: 7\n    }, this), lowerDivCharts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-10 p-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 mb-8 border-b-4 pt-2 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Operational Efficiency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(operationalData === null || operationalData === void 0 ? void 0 : operationalData.FYStartYear, operationalData === null || operationalData === void 0 ? void 0 : operationalData.FYStartMonth), \" | \", formatCompanyName(operationalData === null || operationalData === void 0 ? void 0 : operationalData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 849,\n        columnNumber: 11\n      }, this), lowerDivCharts.map(chart => renderChart(chart))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 847,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 825,\n    columnNumber: 5\n  }, this);\n};\n_s(OperationalEfficiencyDashboard, \"O0Le7Wl/rSJonuZpsA+RcAzhNOs=\");\n_c = OperationalEfficiencyDashboard;\nexport default OperationalEfficiencyDashboard;\nvar _c;\n$RefreshReg$(_c, \"OperationalEfficiencyDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Apex<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "OperationalEfficiencyDashboard", "headerTextStyle", "subHeadingTextStyle", "contentTextStyle", "contentSettings", "operationalData", "_s", "salesOutstandingRef", "payablesOutstandingRef", "inventoryOutstandingRef", "cashConversionRef", "fixedAssetTurnoverRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart<PERSON>ey", "chartSettings", "isDataLoaded", "hasSalesOutstandingData", "daysSalesAROutstanding", "Array", "isArray", "length", "hasPayablesOutstandingData", "daysSalesAPOutstanding", "hasInventoryOutstandingData", "daysInventoryOutstanding", "hasCashConversionData", "cashConversionCycle", "hasFixedAssetTurnoverData", "fixedAssetTurnover", "for<PERSON>ach", "ref", "current", "innerHTML", "initializeCharts", "formatMonthYear", "year", "month", "monthNames", "String", "slice", "_dataToUse$daysSalesA", "_dataToUse$daysSalesA2", "_dataToUse$daysSalesA3", "_dataToUse$daysInvent", "_dataToUse$cashConver", "_dataToUse$fixedAsset", "dataToUse", "operationalEfficiency", "operational", "data", "reportData", "categories", "map", "item", "daysSalesOutstandingData", "value", "dso", "days_sales_outstanding", "daysSalesOutstanding", "parseFloat", "daysPayablesOutstandingData", "dpo", "days_payables_outstanding", "daysPayablesOutstanding", "daysInventoryOutstandingData", "dio", "days_inventory_outstanding", "cashConversionCycleData", "CCC", "ccc", "isNaN", "fixedAssetTurnoverData", "fat", "colors", "salesOutstanding", "payablesOutstanding", "inventoryOutstanding", "cashConversion", "salesOutstandingOptions", "series", "name", "chart", "type", "height", "toolbar", "show", "background", "zoom", "enabled", "dataLabels", "formatter", "val", "Math", "round", "style", "fontSize", "fontWeight", "offsetY", "dropShadow", "stroke", "curve", "width", "fill", "gradient", "shadeIntensity", "colorStops", "offset", "color", "opacity", "markers", "size", "strokeColors", "strokeWidth", "hover", "xaxis", "labels", "axisBorder", "axisTicks", "yaxis", "grid", "padding", "left", "right", "top", "bottom", "legend", "tooltip", "y", "payablesOutstandingOptions", "parentHeightOffset", "sparkline", "inventoryOutstandingOptions", "plotOptions", "bar", "columnWidth", "position", "cashConversionOptions", "fixedAssetTurnoverOptions", "toFixed", "clearAndRender<PERSON>hart", "options", "chartName", "setTimeout", "render", "window", "salesOutstandingChart", "payablesOutstandingChart", "inventoryOutstandingChart", "cashConversionChart", "fixedAssetTurnoverChart", "error", "console", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "some", "formatHeaderPeriod", "startYear", "startMonth", "startMonthName", "formatHeaderStyle", "parseInt", "formatCompanyName", "companyName", "substring", "all<PERSON>hart<PERSON>", "title", "hasData", "_dataToUse", "_dataToUse$daysSalesA4", "_dataToUse2", "_dataToUse2$daysSales", "hasDescription", "description", "content", "_dataToUse3", "_dataToUse3$daysInven", "_dataToUse4", "_dataToUse4$cashConve", "_dataToUse5", "_dataToUse5$fixedAsse", "filter", "upperDivCharts", "lowerDiv<PERSON><PERSON>s", "<PERSON><PERSON><PERSON>", "className", "containerMarginBottom", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYStartYear", "FYStartMonth", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/OperationalEfficiency.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport ApexCharts from 'apexcharts';\r\n\r\nconst OperationalEfficiencyDashboard = ({\r\n  headerTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  contentSettings = {},\r\n  operationalData = null\r\n}) => {\r\n  const salesOutstandingRef = useRef(null);\r\n  const payablesOutstandingRef = useRef(null);\r\n  const inventoryOutstandingRef = useRef(null);\r\n  const cashConversionRef = useRef(null);\r\n  const fixedAssetTurnoverRef = useRef(null);\r\n\r\n  // Function to check if a chart should be displayed based on content settings\r\n  const shouldDisplayChart = (chartKey) => {\r\n    if (!contentSettings?.chartSettings) return true; // Default to true if no settings\r\n    return contentSettings.chartSettings[chartKey] === true;\r\n  };\r\n\r\n  // Enhanced data validation function - more lenient approach like FiscalYear\r\n  const isDataLoaded = () => {\r\n    if (!operationalData) {\r\n      return false;\r\n    }\r\n\r\n    // Check if at least one of the required data arrays exists and has content\r\n    const hasSalesOutstandingData = operationalData.daysSalesAROutstanding &&\r\n      Array.isArray(operationalData.daysSalesAROutstanding) &&\r\n      operationalData.daysSalesAROutstanding.length > 0;\r\n\r\n    const hasPayablesOutstandingData = operationalData.daysSalesAPOutstanding &&\r\n      Array.isArray(operationalData.daysSalesAPOutstanding) &&\r\n      operationalData.daysSalesAPOutstanding.length > 0;\r\n\r\n    const hasInventoryOutstandingData = operationalData.daysInventoryOutstanding &&\r\n      Array.isArray(operationalData.daysInventoryOutstanding) &&\r\n      operationalData.daysInventoryOutstanding.length > 0;\r\n\r\n    const hasCashConversionData = operationalData.cashConversionCycle &&\r\n      Array.isArray(operationalData.cashConversionCycle) &&\r\n      operationalData.cashConversionCycle.length > 0;\r\n\r\n    const hasFixedAssetTurnoverData = operationalData.fixedAssetTurnover &&\r\n      Array.isArray(operationalData.fixedAssetTurnover) &&\r\n      operationalData.fixedAssetTurnover.length > 0;\r\n\r\n    return hasSalesOutstandingData || hasPayablesOutstandingData || hasInventoryOutstandingData ||\r\n      hasCashConversionData || hasFixedAssetTurnoverData;\r\n  };\r\n\r\n  // Function to check if we have any meaningful data (not all zeros) - like FiscalYear\r\n\r\n  useEffect(() => {\r\n    if (isDataLoaded()) {\r\n      // Clear charts first\r\n      [salesOutstandingRef, payablesOutstandingRef, inventoryOutstandingRef, cashConversionRef, fixedAssetTurnoverRef].forEach((ref) => {\r\n        if (ref.current) {\r\n          ref.current.innerHTML = \"\";\r\n        }\r\n      });\r\n      // Initialize charts with new data\r\n      initializeCharts();\r\n    }\r\n  }, [operationalData, contentSettings]);\r\n\r\n  const formatMonthYear = (year, month) => {\r\n    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',\r\n      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\r\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\r\n  };\r\n\r\n  const initializeCharts = () => {\r\n    if (!operationalData) return;\r\n\r\n    // Try to find the correct data structure\r\n    let dataToUse = operationalData;\r\n\r\n    // Check if data is nested under different possible paths\r\n    if (operationalData.operationalEfficiency) {\r\n      dataToUse = operationalData.operationalEfficiency;\r\n    } else if (operationalData.operational) {\r\n      dataToUse = operationalData.operational;\r\n    } else if (operationalData.data) {\r\n      dataToUse = operationalData.data;\r\n    } else if (operationalData.reportData) {\r\n      dataToUse = operationalData.reportData;\r\n    }\r\n\r\n    // Prepare data from API response - using correct property names from your JSON\r\n    const categories = dataToUse.daysSalesAROutstanding?.map(item =>\r\n      formatMonthYear(item.year, item.month)\r\n    ) || [];\r\n\r\n    const daysSalesOutstandingData = dataToUse.daysSalesAROutstanding?.map(item => {\r\n      const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n    const daysPayablesOutstandingData = dataToUse.daysSalesAPOutstanding?.map(item => {\r\n      const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n    const daysInventoryOutstandingData = dataToUse.daysInventoryOutstanding?.map(item => {\r\n      const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n    const cashConversionCycleData = dataToUse.cashConversionCycle?.map(item => {\r\n      const value = parseFloat(item.CCC || item.ccc);\r\n      return isNaN(value) || value === null ? 0 : value;\r\n    }) || [];\r\n\r\n    const fixedAssetTurnoverData = dataToUse.fixedAssetTurnover?.map(item =>\r\n      parseFloat(item.fat) || 0\r\n    ) || [];\r\n\r\n\r\n    // Color scheme\r\n    const colors = {\r\n      salesOutstanding: '#2d6a9b',\r\n      payablesOutstanding: '#565aa4',\r\n      inventoryOutstanding: '#2a689a',\r\n      cashConversion: '#ff6b47',\r\n      fixedAssetTurnover: '#2d6a9b'\r\n    };\r\n\r\n    // 1. Days Sales (A/R) Outstanding Chart\r\n    const salesOutstandingOptions = {\r\n      series: [{\r\n        name: 'Days Sales Outstanding',\r\n        data: daysSalesOutstandingData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 200,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return Math.round(val);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: [colors.salesOutstanding]\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: colors.salesOutstanding, opacity: 0.4 },\r\n            { offset: 100, color: colors.salesOutstanding, opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: [colors.salesOutstanding],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.salesOutstanding],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: {\r\n        show: false\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return Math.round(val) + ' days';\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // 2. Days Payables (AP) Outstanding Chart\r\n    const payablesOutstandingOptions = {\r\n      series: [{\r\n        name: 'Days Payables Outstanding',\r\n        data: daysPayablesOutstandingData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 200,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        parentHeightOffset: 0,\r\n        sparkline: {\r\n          enabled: false\r\n        },\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return Math.round(val);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: [colors.payablesOutstanding]\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: colors.payablesOutstanding, opacity: 0.4 },\r\n            { offset: 100, color: colors.payablesOutstanding, opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: [colors.payablesOutstanding],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.payablesOutstanding],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: {\r\n        show: false\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return Math.round(val) + ' days';\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // 3. Days Inventory Outstanding Chart (Bar Chart)\r\n    const inventoryOutstandingOptions = {\r\n      series: [{\r\n        name: 'Days Inventory Outstanding',\r\n        data: daysInventoryOutstandingData\r\n      }],\r\n      chart: {\r\n        type: 'bar',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent'\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          columnWidth: '40%',\r\n          dataLabels: {\r\n            position: 'top'\r\n          }\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return Math.round(val);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -20,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.inventoryOutstanding],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 10,\r\n          right: 10,\r\n          top: 25,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: {\r\n        show: false\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return Math.round(val) + ' days';\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // 4. Cash Conversion Cycle Chart\r\n    const cashConversionOptions = {\r\n      series: [{\r\n        name: 'Cash Conversion Cycle',\r\n        data: cashConversionCycleData\r\n      }],\r\n      chart: {\r\n        type: 'line',\r\n        height: 200,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return Math.round(val);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        width: 2,\r\n        colors: [colors.cashConversion]\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: [colors.cashConversion],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.cashConversion],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: {\r\n        show: false\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return Math.round(val) + ' days';\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // 5. Fixed Asset Turnover Chart\r\n    const fixedAssetTurnoverOptions = {\r\n      series: [{\r\n        name: 'Fixed Asset Turnover',\r\n        data: fixedAssetTurnoverData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 200,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return val.toFixed(2);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: [colors.fixedAssetTurnover]\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: colors.fixedAssetTurnover, opacity: 0.4 },\r\n            { offset: 100, color: colors.fixedAssetTurnover, opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: [colors.fixedAssetTurnover],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.fixedAssetTurnover],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 15,\r\n          right: 15,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: {\r\n        show: false\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return val.toFixed(2);\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // Clear existing charts before rendering new ones\r\n    const clearAndRenderChart = (ref, options, chartName) => {\r\n      if (ref.current) {\r\n        // Clear any existing chart\r\n        ref.current.innerHTML = '';\r\n\r\n        // Wait a tick before rendering to ensure DOM is cleared\r\n        setTimeout(() => {\r\n          if (ref.current) {\r\n            try {\r\n              const chart = new ApexCharts(ref.current, options);\r\n              chart.render();\r\n\r\n              // Store chart instances globally for export\r\n              if (chartName === \"Days Sales Outstanding\") {\r\n                window.salesOutstandingChart = chart;\r\n              } else if (chartName === \"Days Payables Outstanding\") {\r\n                window.payablesOutstandingChart = chart;\r\n              } else if (chartName === \"Days Inventory Outstanding\") {\r\n                window.inventoryOutstandingChart = chart;\r\n              } else if (chartName === \"Cash Conversion Cycle\") {\r\n                window.cashConversionChart = chart;\r\n              } else if (chartName === \"Fixed Asset Turnover\") {\r\n                window.fixedAssetTurnoverChart = chart;\r\n              }\r\n            } catch (error) {\r\n              console.error(`OperationalEfficiency - Error rendering ${chartName} chart:`, error);\r\n              // Show error message in chart container\r\n              ref.current.innerHTML = `<div class=\"flex items-center justify-center h-48 text-gray-500\">Error loading ${chartName} chart</div>`;\r\n            }\r\n          }\r\n        }, 10);\r\n      }\r\n    };\r\n\r\n    // Get enabled charts and assign chart options\r\n    const enabledCharts = getEnabledCharts();\r\n\r\n    // Assign chart options to enabled charts\r\n    enabledCharts.forEach(chart => {\r\n      switch (chart.key) {\r\n        case 'daysSalesOutstanding':\r\n          chart.options = salesOutstandingOptions;\r\n          chart.name = 'Days Sales Outstanding';\r\n          break;\r\n        case 'daysPayablesOutstanding':\r\n          chart.options = payablesOutstandingOptions;\r\n          chart.name = 'Days Payables Outstanding';\r\n          break;\r\n        case 'daysInventoryOutstanding':\r\n          chart.options = inventoryOutstandingOptions;\r\n          chart.name = 'Days Inventory Outstanding';\r\n          break;\r\n        case 'cashConversionCycle':\r\n          chart.options = cashConversionOptions;\r\n          chart.name = 'Cash Conversion Cycle';\r\n          break;\r\n        case 'fixedAssetTurnover':\r\n          chart.options = fixedAssetTurnoverOptions;\r\n          chart.name = 'Fixed Asset Turnover';\r\n          break;\r\n      }\r\n    });\r\n\r\n    // Render the enabled charts with error handling\r\n    enabledCharts.forEach(({ ref, options, name }) => {\r\n      if (ref.current) {\r\n        clearAndRenderChart(ref, options, name);\r\n      }\r\n    });\r\n\r\n    // Clear all chart containers that are not being used\r\n    [salesOutstandingRef, payablesOutstandingRef, inventoryOutstandingRef, cashConversionRef, fixedAssetTurnoverRef].forEach((ref) => {\r\n      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {\r\n        ref.current.innerHTML = \"\";\r\n      }\r\n    });\r\n  };\r\n\r\n  const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  const formatHeaderStyle = () => {\r\n    const style = { ...headerTextStyle };\r\n    \r\n    if (style.fontSize) {\r\n      const fontSize = parseInt(style.fontSize);\r\n      style.fontSize = `${fontSize / 2}px`;\r\n    }\r\n    return style;\r\n  };\r\n\r\n  const formatCompanyName = (companyName) => {\r\n    if (!companyName) return '';\r\n\r\n    if (companyName.length > 15) {\r\n      return companyName.substring(0, 15) + '...';\r\n    }\r\n\r\n    return companyName;\r\n  };\r\n\r\n  // Function to determine which charts should be rendered and their order\r\n  const getEnabledCharts = () => {\r\n    // Try to find the correct data structure\r\n    let dataToUse = operationalData;\r\n    if (operationalData?.operationalEfficiency) {\r\n      dataToUse = operationalData.operationalEfficiency;\r\n    } else if (operationalData?.operational) {\r\n      dataToUse = operationalData.operational;\r\n    } else if (operationalData?.data) {\r\n      dataToUse = operationalData.data;\r\n    } else if (operationalData?.reportData) {\r\n      dataToUse = operationalData.reportData;\r\n    }\r\n\r\n    const allCharts = [\r\n      {\r\n        key: 'daysSalesOutstanding',\r\n        title: 'Days Sales (A/R) Outstanding',\r\n        ref: salesOutstandingRef,\r\n        options: null, // Will be set in initializeCharts\r\n        hasData: () => {\r\n          const daysSalesOutstandingData = dataToUse?.daysSalesAROutstanding?.map(item => {\r\n            const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;\r\n            return parseFloat(value) || 0;\r\n          }) || [];\r\n          return daysSalesOutstandingData.length > 0 && daysSalesOutstandingData.some(val => val > 0);\r\n        }\r\n      },\r\n      {\r\n        key: 'daysPayablesOutstanding',\r\n        title: 'Days Payables (AP) Outstanding',\r\n        ref: payablesOutstandingRef,\r\n        options: null,\r\n        hasData: () => {\r\n          const daysPayablesOutstandingData = dataToUse?.daysSalesAPOutstanding?.map(item => {\r\n            const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;\r\n            return parseFloat(value) || 0;\r\n          }) || [];\r\n          return daysPayablesOutstandingData.length > 0 && daysPayablesOutstandingData.some(val => val > 0);\r\n        },\r\n        hasDescription: true,\r\n        description: {\r\n          title: 'Days AR Outstanding & Days AP Outstanding',\r\n          content: 'Average number of days it takes customers to pay for invoices/ average number of days it takes a company to pay its suppliers.'\r\n        }\r\n      },\r\n      {\r\n        key: 'daysInventoryOutstanding',\r\n        title: 'Days Inventory Outstanding',\r\n        ref: inventoryOutstandingRef,\r\n        options: null,\r\n        hasData: () => {\r\n          const daysInventoryOutstandingData = dataToUse?.daysInventoryOutstanding?.map(item => {\r\n            const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;\r\n            return parseFloat(value) || 0;\r\n          }) || [];\r\n          return daysInventoryOutstandingData.length > 0 && daysInventoryOutstandingData.some(val => val > 0);\r\n        },\r\n        // containerMarginBottom: 'mb-24'\r\n      },\r\n      {\r\n        key: 'cashConversionCycle',\r\n        title: 'Cash Conversion Cycle',\r\n        ref: cashConversionRef,\r\n        options: null,\r\n        hasData: () => {\r\n          const cashConversionCycleData = dataToUse?.cashConversionCycle?.map(item => {\r\n            const value = parseFloat(item.CCC || item.ccc);\r\n            return isNaN(value) || value === null ? 0 : value;\r\n          }) || [];\r\n          return cashConversionCycleData.length > 0 && cashConversionCycleData.some(val => val != null && val !== 0);\r\n        },\r\n        hasDescription: true,\r\n        description: {\r\n          title: 'Cash Conversion Cycle (CCC)',\r\n          content: 'The time it takes a company to convert the money spent on inventory or production back into cash by selling its goods or services. A shorter CCC is better because it means less time that money is tied up in inventory or accounts receivable.'\r\n        }\r\n      },\r\n      {\r\n        key: 'fixedAssetTurnover',\r\n        title: 'Fixed Asset Turnover',\r\n        ref: fixedAssetTurnoverRef,\r\n        options: null,\r\n        hasData: () => {\r\n          const fixedAssetTurnoverData = dataToUse?.fixedAssetTurnover?.map(item =>\r\n            parseFloat(item.fat) || 0\r\n          ) || [];\r\n          return fixedAssetTurnoverData.length > 0 && fixedAssetTurnoverData.some(val => val > 0);\r\n        },\r\n        hasDescription: true,\r\n        description: {\r\n          title: 'Fixed Asset Turnover (FAT)',\r\n          content: 'The ratio of a company\\'s net sales to its average fixed assets over a specific period, usually a year. A higher ratio indicates that a company is using its fixed assets more efficiently, while a lower ratio suggests underutilization.'\r\n        }\r\n      }\r\n    ];\r\n\r\n    // Filter charts based on settings and data availability\r\n    return allCharts.filter(chart =>\r\n      shouldDisplayChart(chart.key) && chart.hasData()\r\n    );\r\n  };\r\n\r\n  // Get enabled charts for dynamic layout\r\n  const enabledCharts = getEnabledCharts();\r\n\r\n  // Split charts between upper and lower divs\r\n  // Upper div can hold up to 3 charts, lower div gets the rest\r\n  const upperDivCharts = enabledCharts.slice(0, 3);\r\n  const lowerDivCharts = enabledCharts.slice(3);\r\n\r\n  // Helper function to render a chart component\r\n  const renderChart = (chart) => (\r\n    <div key={chart.key} className={`bg-white p-6 border-b-4 border-blue-900 ${chart.containerMarginBottom || ''}`}>\r\n      <div\r\n        className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n        style={subHeadingTextStyle}\r\n      >\r\n        {chart.title}\r\n      </div>\r\n      <div ref={chart.ref} className=\"mb-5\"></div>\r\n      {chart.hasDescription && (\r\n        <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\">\r\n          <div\r\n            className=\"text-teal-600 text-2xl mb-2\"\r\n            style={{ ...subHeadingTextStyle, fontWeight: 'lighter' }}\r\n          >\r\n            {chart.description.title}\r\n          </div>\r\n          <div style={contentTextStyle}>\r\n            {chart.description.content}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"p-5\">\r\n      <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-8 p-10 mb-2\">\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4  border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n            Operational Efficiency\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n            {formatHeaderPeriod(operationalData?.FYStartYear, operationalData?.FYStartMonth)} | {formatCompanyName(operationalData?.companyName)}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Dynamically render charts for upper div (up to 3 charts) */}\r\n        {upperDivCharts.map(chart => renderChart(chart))}\r\n\r\n      </div>\r\n\r\n      {/* Only render lower div if there are charts to display (4th chart onwards) */}\r\n      {lowerDivCharts.length > 0 && (\r\n        <div className=\"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-10 p-10\">\r\n          {/* Header Section */}\r\n          <div className=\"component-header flex items-center justify-between gap-4 mb-8 border-b-4 pt-2 border-blue-900 pb-2\">\r\n            <h1\r\n              className=\"text-4xl font-bold text-gray-800 m-0\"\r\n              style={headerTextStyle}\r\n            >\r\n              Operational Efficiency\r\n            </h1>\r\n            <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n              {formatHeaderPeriod(operationalData?.FYStartYear, operationalData?.FYStartMonth)} | {formatCompanyName(operationalData?.companyName)}\r\n            </p>\r\n          </div>\r\n\r\n          {/* Dynamically render charts for lower div */}\r\n          {lowerDivCharts.map(chart => renderChart(chart))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OperationalEfficiencyDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,UAAU,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,8BAA8B,GAAGA,CAAC;EACtCC,eAAe,GAAG,CAAC,CAAC;EACpBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,eAAe,GAAG,CAAC,CAAC;EACpBC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,mBAAmB,GAAGX,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMY,sBAAsB,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAMa,uBAAuB,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAMc,iBAAiB,GAAGd,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMe,qBAAqB,GAAGf,MAAM,CAAC,IAAI,CAAC;;EAE1C;EACA,MAAMgB,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,EAACT,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEU,aAAa,GAAE,OAAO,IAAI,CAAC,CAAC;IAClD,OAAOV,eAAe,CAACU,aAAa,CAACD,QAAQ,CAAC,KAAK,IAAI;EACzD,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACV,eAAe,EAAE;MACpB,OAAO,KAAK;IACd;;IAEA;IACA,MAAMW,uBAAuB,GAAGX,eAAe,CAACY,sBAAsB,IACpEC,KAAK,CAACC,OAAO,CAACd,eAAe,CAACY,sBAAsB,CAAC,IACrDZ,eAAe,CAACY,sBAAsB,CAACG,MAAM,GAAG,CAAC;IAEnD,MAAMC,0BAA0B,GAAGhB,eAAe,CAACiB,sBAAsB,IACvEJ,KAAK,CAACC,OAAO,CAACd,eAAe,CAACiB,sBAAsB,CAAC,IACrDjB,eAAe,CAACiB,sBAAsB,CAACF,MAAM,GAAG,CAAC;IAEnD,MAAMG,2BAA2B,GAAGlB,eAAe,CAACmB,wBAAwB,IAC1EN,KAAK,CAACC,OAAO,CAACd,eAAe,CAACmB,wBAAwB,CAAC,IACvDnB,eAAe,CAACmB,wBAAwB,CAACJ,MAAM,GAAG,CAAC;IAErD,MAAMK,qBAAqB,GAAGpB,eAAe,CAACqB,mBAAmB,IAC/DR,KAAK,CAACC,OAAO,CAACd,eAAe,CAACqB,mBAAmB,CAAC,IAClDrB,eAAe,CAACqB,mBAAmB,CAACN,MAAM,GAAG,CAAC;IAEhD,MAAMO,yBAAyB,GAAGtB,eAAe,CAACuB,kBAAkB,IAClEV,KAAK,CAACC,OAAO,CAACd,eAAe,CAACuB,kBAAkB,CAAC,IACjDvB,eAAe,CAACuB,kBAAkB,CAACR,MAAM,GAAG,CAAC;IAE/C,OAAOJ,uBAAuB,IAAIK,0BAA0B,IAAIE,2BAA2B,IACzFE,qBAAqB,IAAIE,yBAAyB;EACtD,CAAC;;EAED;;EAEAhC,SAAS,CAAC,MAAM;IACd,IAAIoB,YAAY,CAAC,CAAC,EAAE;MAClB;MACA,CAACR,mBAAmB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,qBAAqB,CAAC,CAACkB,OAAO,CAAEC,GAAG,IAAK;QAChI,IAAIA,GAAG,CAACC,OAAO,EAAE;UACfD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;QAC5B;MACF,CAAC,CAAC;MACF;MACAC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC5B,eAAe,EAAED,eAAe,CAAC,CAAC;EAEtC,MAAM8B,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACvC,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAC1D,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC3C,OAAO,GAAGA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAIE,MAAM,CAACH,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC;EAED,MAAMN,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAO,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAC7B,IAAI,CAACxC,eAAe,EAAE;;IAEtB;IACA,IAAIyC,SAAS,GAAGzC,eAAe;;IAE/B;IACA,IAAIA,eAAe,CAAC0C,qBAAqB,EAAE;MACzCD,SAAS,GAAGzC,eAAe,CAAC0C,qBAAqB;IACnD,CAAC,MAAM,IAAI1C,eAAe,CAAC2C,WAAW,EAAE;MACtCF,SAAS,GAAGzC,eAAe,CAAC2C,WAAW;IACzC,CAAC,MAAM,IAAI3C,eAAe,CAAC4C,IAAI,EAAE;MAC/BH,SAAS,GAAGzC,eAAe,CAAC4C,IAAI;IAClC,CAAC,MAAM,IAAI5C,eAAe,CAAC6C,UAAU,EAAE;MACrCJ,SAAS,GAAGzC,eAAe,CAAC6C,UAAU;IACxC;;IAEA;IACA,MAAMC,UAAU,GAAG,EAAAX,qBAAA,GAAAM,SAAS,CAAC7B,sBAAsB,cAAAuB,qBAAA,uBAAhCA,qBAAA,CAAkCY,GAAG,CAACC,IAAI,IAC3DnB,eAAe,CAACmB,IAAI,CAAClB,IAAI,EAAEkB,IAAI,CAACjB,KAAK,CACvC,CAAC,KAAI,EAAE;IAEP,MAAMkB,wBAAwB,GAAG,EAAAb,sBAAA,GAAAK,SAAS,CAAC7B,sBAAsB,cAAAwB,sBAAA,uBAAhCA,sBAAA,CAAkCW,GAAG,CAACC,IAAI,IAAI;MAC7E,MAAME,KAAK,GAAGF,IAAI,CAACG,GAAG,IAAIH,IAAI,CAACI,sBAAsB,IAAIJ,IAAI,CAACK,oBAAoB,IAAI,CAAC;MACvF,OAAOC,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IAER,MAAMK,2BAA2B,GAAG,EAAAlB,sBAAA,GAAAI,SAAS,CAACxB,sBAAsB,cAAAoB,sBAAA,uBAAhCA,sBAAA,CAAkCU,GAAG,CAACC,IAAI,IAAI;MAChF,MAAME,KAAK,GAAGF,IAAI,CAACQ,GAAG,IAAIR,IAAI,CAACS,yBAAyB,IAAIT,IAAI,CAACU,uBAAuB,IAAI,CAAC;MAC7F,OAAOJ,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IAER,MAAMS,4BAA4B,GAAG,EAAArB,qBAAA,GAAAG,SAAS,CAACtB,wBAAwB,cAAAmB,qBAAA,uBAAlCA,qBAAA,CAAoCS,GAAG,CAACC,IAAI,IAAI;MACnF,MAAME,KAAK,GAAGF,IAAI,CAACY,GAAG,IAAIZ,IAAI,CAACa,0BAA0B,IAAIb,IAAI,CAAC7B,wBAAwB,IAAI,CAAC;MAC/F,OAAOmC,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IAER,MAAMY,uBAAuB,GAAG,EAAAvB,qBAAA,GAAAE,SAAS,CAACpB,mBAAmB,cAAAkB,qBAAA,uBAA7BA,qBAAA,CAA+BQ,GAAG,CAACC,IAAI,IAAI;MACzE,MAAME,KAAK,GAAGI,UAAU,CAACN,IAAI,CAACe,GAAG,IAAIf,IAAI,CAACgB,GAAG,CAAC;MAC9C,OAAOC,KAAK,CAACf,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,GAAG,CAAC,GAAGA,KAAK;IACnD,CAAC,CAAC,KAAI,EAAE;IAER,MAAMgB,sBAAsB,GAAG,EAAA1B,qBAAA,GAAAC,SAAS,CAAClB,kBAAkB,cAAAiB,qBAAA,uBAA5BA,qBAAA,CAA8BO,GAAG,CAACC,IAAI,IACnEM,UAAU,CAACN,IAAI,CAACmB,GAAG,CAAC,IAAI,CAC1B,CAAC,KAAI,EAAE;;IAGP;IACA,MAAMC,MAAM,GAAG;MACbC,gBAAgB,EAAE,SAAS;MAC3BC,mBAAmB,EAAE,SAAS;MAC9BC,oBAAoB,EAAE,SAAS;MAC/BC,cAAc,EAAE,SAAS;MACzBjD,kBAAkB,EAAE;IACtB,CAAC;;IAED;IACA,MAAMkD,uBAAuB,GAAG;MAC9BC,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,wBAAwB;QAC9B/B,IAAI,EAAEK;MACR,CAAC,CAAC;MACF2B,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzBC,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;QACxB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBtB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBuB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZX,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDU,UAAU,EAAE;UACVV,OAAO,EAAE;QACX;MACF,CAAC;MACDW,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACR5B,MAAM,EAAE,CAACA,MAAM,CAACC,gBAAgB;MAClC,CAAC;MACD4B,IAAI,EAAE;QACJpB,IAAI,EAAE,UAAU;QAChBqB,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjBtB,IAAI,EAAE,UAAU;UAChBuB,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAElC,MAAM,CAACC,gBAAgB;YAAEkC,OAAO,EAAE;UAAI,CAAC,EAC3D;YAAEF,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAElC,MAAM,CAACC,gBAAgB;YAAEkC,OAAO,EAAE;UAAI,CAAC;QAEjE;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPrC,MAAM,EAAE,CAACA,MAAM,CAACC,gBAAgB,CAAC;QACjCqC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDI,KAAK,EAAE;QACL/D,UAAU,EAAEA,UAAU;QACtBgE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLrB,MAAM,EAAE,MAAM;YACdsB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE/B,IAAI,EAAE;QAAM,CAAC;QAC3BgC,SAAS,EAAE;UAAEhC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDiC,KAAK,EAAE;QACLjC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAACC,gBAAgB,CAAC;MACjC6C,IAAI,EAAE;QACJlC,IAAI,EAAE,KAAK;QACXmC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNxC,IAAI,EAAE;MACR,CAAC;MACDyC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC,GAAG,OAAO;UAClC;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAMqC,0BAA0B,GAAG;MACjCjD,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,2BAA2B;QACjC/B,IAAI,EAAEW;MACR,CAAC,CAAC;MACFqB,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzB2C,kBAAkB,EAAE,CAAC;QACrBC,SAAS,EAAE;UACT1C,OAAO,EAAE;QACX,CAAC;QACDD,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;QACxB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBtB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBuB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZX,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDU,UAAU,EAAE;UACVV,OAAO,EAAE;QACX;MACF,CAAC;MACDW,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACR5B,MAAM,EAAE,CAACA,MAAM,CAACE,mBAAmB;MACrC,CAAC;MACD2B,IAAI,EAAE;QACJpB,IAAI,EAAE,UAAU;QAChBqB,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjBtB,IAAI,EAAE,UAAU;UAChBuB,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAElC,MAAM,CAACE,mBAAmB;YAAEiC,OAAO,EAAE;UAAI,CAAC,EAC9D;YAAEF,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAElC,MAAM,CAACE,mBAAmB;YAAEiC,OAAO,EAAE;UAAI,CAAC;QAEpE;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPrC,MAAM,EAAE,CAACA,MAAM,CAACE,mBAAmB,CAAC;QACpCoC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDI,KAAK,EAAE;QACL/D,UAAU,EAAEA,UAAU;QACtBgE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLrB,MAAM,EAAE,MAAM;YACdsB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE/B,IAAI,EAAE;QAAM,CAAC;QAC3BgC,SAAS,EAAE;UAAEhC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDiC,KAAK,EAAE;QACLjC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAACE,mBAAmB,CAAC;MACpC4C,IAAI,EAAE;QACJlC,IAAI,EAAE,KAAK;QACXmC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNxC,IAAI,EAAE;MACR,CAAC;MACDyC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC,GAAG,OAAO;UAClC;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAMwC,2BAA2B,GAAG;MAClCpD,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,4BAA4B;QAClC/B,IAAI,EAAEe;MACR,CAAC,CAAC;MACFiB,KAAK,EAAE;QACLC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACD8C,WAAW,EAAE;QACXC,GAAG,EAAE;UACHC,WAAW,EAAE,KAAK;UAClB7C,UAAU,EAAE;YACV8C,QAAQ,EAAE;UACZ;QACF;MACF,CAAC;MACD9C,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;QACxB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBtB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBuB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZX,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDU,UAAU,EAAE;UACVV,OAAO,EAAE;QACX;MACF,CAAC;MACD0B,KAAK,EAAE;QACL/D,UAAU,EAAEA,UAAU;QACtBgE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLrB,MAAM,EAAE,MAAM;YACdsB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE/B,IAAI,EAAE;QAAM,CAAC;QAC3BgC,SAAS,EAAE;UAAEhC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDiC,KAAK,EAAE;QACLjC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAACG,oBAAoB,CAAC;MACrC2C,IAAI,EAAE;QACJlC,IAAI,EAAE,KAAK;QACXmC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNxC,IAAI,EAAE;MACR,CAAC;MACDyC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC,GAAG,OAAO;UAClC;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAM6C,qBAAqB,GAAG;MAC5BzD,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,uBAAuB;QAC7B/B,IAAI,EAAEkB;MACR,CAAC,CAAC;MACFc,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzBC,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;QACxB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBtB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBuB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZX,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDU,UAAU,EAAE;UACVV,OAAO,EAAE;QACX;MACF,CAAC;MACDW,MAAM,EAAE;QACNE,KAAK,EAAE,CAAC;QACR5B,MAAM,EAAE,CAACA,MAAM,CAACI,cAAc;MAChC,CAAC;MACDgC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPrC,MAAM,EAAE,CAACA,MAAM,CAACI,cAAc,CAAC;QAC/BkC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDI,KAAK,EAAE;QACL/D,UAAU,EAAEA,UAAU;QACtBgE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLrB,MAAM,EAAE,MAAM;YACdsB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE/B,IAAI,EAAE;QAAM,CAAC;QAC3BgC,SAAS,EAAE;UAAEhC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDiC,KAAK,EAAE;QACLjC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAACI,cAAc,CAAC;MAC/B0C,IAAI,EAAE;QACJlC,IAAI,EAAE,KAAK;QACXmC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNxC,IAAI,EAAE;MACR,CAAC;MACDyC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC,GAAG,OAAO;UAClC;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAM8C,yBAAyB,GAAG;MAChC1D,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,sBAAsB;QAC5B/B,IAAI,EAAEsB;MACR,CAAC,CAAC;MACFU,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzBC,IAAI,EAAE;UACJC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE;QACVD,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOA,GAAG,CAAC+C,OAAO,CAAC,CAAC,CAAC;QACvB,CAAC;QACD5C,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBtB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBuB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZX,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDU,UAAU,EAAE;UACVV,OAAO,EAAE;QACX;MACF,CAAC;MACDW,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACR5B,MAAM,EAAE,CAACA,MAAM,CAAC7C,kBAAkB;MACpC,CAAC;MACD0E,IAAI,EAAE;QACJpB,IAAI,EAAE,UAAU;QAChBqB,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjBtB,IAAI,EAAE,UAAU;UAChBuB,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAElC,MAAM,CAAC7C,kBAAkB;YAAEgF,OAAO,EAAE;UAAI,CAAC,EAC7D;YAAEF,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAElC,MAAM,CAAC7C,kBAAkB;YAAEgF,OAAO,EAAE;UAAI,CAAC;QAEnE;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPrC,MAAM,EAAE,CAACA,MAAM,CAAC7C,kBAAkB,CAAC;QACnCmF,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDI,KAAK,EAAE;QACL/D,UAAU,EAAEA,UAAU;QACtBgE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLrB,MAAM,EAAE,MAAM;YACdsB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE/B,IAAI,EAAE;QAAM,CAAC;QAC3BgC,SAAS,EAAE;UAAEhC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDiC,KAAK,EAAE;QACLjC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAAC7C,kBAAkB,CAAC;MACnC2F,IAAI,EAAE;QACJlC,IAAI,EAAE,KAAK;QACXmC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNxC,IAAI,EAAE;MACR,CAAC;MACDyC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,OAAOA,GAAG,CAAC+C,OAAO,CAAC,CAAC,CAAC;UACvB;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAGA,CAAC7G,GAAG,EAAE8G,OAAO,EAAEC,SAAS,KAAK;MACvD,IAAI/G,GAAG,CAACC,OAAO,EAAE;QACf;QACAD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;;QAE1B;QACA8G,UAAU,CAAC,MAAM;UACf,IAAIhH,GAAG,CAACC,OAAO,EAAE;YACf,IAAI;cACF,MAAMkD,KAAK,GAAG,IAAIpF,UAAU,CAACiC,GAAG,CAACC,OAAO,EAAE6G,OAAO,CAAC;cAClD3D,KAAK,CAAC8D,MAAM,CAAC,CAAC;;cAEd;cACA,IAAIF,SAAS,KAAK,wBAAwB,EAAE;gBAC1CG,MAAM,CAACC,qBAAqB,GAAGhE,KAAK;cACtC,CAAC,MAAM,IAAI4D,SAAS,KAAK,2BAA2B,EAAE;gBACpDG,MAAM,CAACE,wBAAwB,GAAGjE,KAAK;cACzC,CAAC,MAAM,IAAI4D,SAAS,KAAK,4BAA4B,EAAE;gBACrDG,MAAM,CAACG,yBAAyB,GAAGlE,KAAK;cAC1C,CAAC,MAAM,IAAI4D,SAAS,KAAK,uBAAuB,EAAE;gBAChDG,MAAM,CAACI,mBAAmB,GAAGnE,KAAK;cACpC,CAAC,MAAM,IAAI4D,SAAS,KAAK,sBAAsB,EAAE;gBAC/CG,MAAM,CAACK,uBAAuB,GAAGpE,KAAK;cACxC;YACF,CAAC,CAAC,OAAOqE,KAAK,EAAE;cACdC,OAAO,CAACD,KAAK,CAAC,2CAA2CT,SAAS,SAAS,EAAES,KAAK,CAAC;cACnF;cACAxH,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,kFAAkF6G,SAAS,cAAc;YACnI;UACF;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC;;IAED;IACA,MAAMW,aAAa,GAAGC,gBAAgB,CAAC,CAAC;;IAExC;IACAD,aAAa,CAAC3H,OAAO,CAACoD,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACyE,GAAG;QACf,KAAK,sBAAsB;UACzBzE,KAAK,CAAC2D,OAAO,GAAG9D,uBAAuB;UACvCG,KAAK,CAACD,IAAI,GAAG,wBAAwB;UACrC;QACF,KAAK,yBAAyB;UAC5BC,KAAK,CAAC2D,OAAO,GAAGZ,0BAA0B;UAC1C/C,KAAK,CAACD,IAAI,GAAG,2BAA2B;UACxC;QACF,KAAK,0BAA0B;UAC7BC,KAAK,CAAC2D,OAAO,GAAGT,2BAA2B;UAC3ClD,KAAK,CAACD,IAAI,GAAG,4BAA4B;UACzC;QACF,KAAK,qBAAqB;UACxBC,KAAK,CAAC2D,OAAO,GAAGJ,qBAAqB;UACrCvD,KAAK,CAACD,IAAI,GAAG,uBAAuB;UACpC;QACF,KAAK,oBAAoB;UACvBC,KAAK,CAAC2D,OAAO,GAAGH,yBAAyB;UACzCxD,KAAK,CAACD,IAAI,GAAG,sBAAsB;UACnC;MACJ;IACF,CAAC,CAAC;;IAEF;IACAwE,aAAa,CAAC3H,OAAO,CAAC,CAAC;MAAEC,GAAG;MAAE8G,OAAO;MAAE5D;IAAK,CAAC,KAAK;MAChD,IAAIlD,GAAG,CAACC,OAAO,EAAE;QACf4G,mBAAmB,CAAC7G,GAAG,EAAE8G,OAAO,EAAE5D,IAAI,CAAC;MACzC;IACF,CAAC,CAAC;;IAEF;IACA,CAACzE,mBAAmB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,qBAAqB,CAAC,CAACkB,OAAO,CAAEC,GAAG,IAAK;MAChI,IAAIA,GAAG,CAACC,OAAO,IAAI,CAACyH,aAAa,CAACG,IAAI,CAAC1E,KAAK,IAAIA,KAAK,CAACnD,GAAG,KAAKA,GAAG,CAAC,EAAE;QAClEA,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;MAC5B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM4H,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACpD,MAAMzH,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAACwH,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAMC,cAAc,GAAG1H,UAAU,CAACyH,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGC,cAAc,IAAIF,SAAS,EAAE;EACzC,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMlE,KAAK,GAAG;MAAE,GAAG7F;IAAgB,CAAC;IAEpC,IAAI6F,KAAK,CAACC,QAAQ,EAAE;MAClB,MAAMA,QAAQ,GAAGkE,QAAQ,CAACnE,KAAK,CAACC,QAAQ,CAAC;MACzCD,KAAK,CAACC,QAAQ,GAAG,GAAGA,QAAQ,GAAG,CAAC,IAAI;IACtC;IACA,OAAOD,KAAK;EACd,CAAC;EAED,MAAMoE,iBAAiB,GAAIC,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,IAAIA,WAAW,CAAC/I,MAAM,GAAG,EAAE,EAAE;MAC3B,OAAO+I,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IAC7C;IAEA,OAAOD,WAAW;EACpB,CAAC;;EAED;EACA,MAAMV,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,IAAI3G,SAAS,GAAGzC,eAAe;IAC/B,IAAIA,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE0C,qBAAqB,EAAE;MAC1CD,SAAS,GAAGzC,eAAe,CAAC0C,qBAAqB;IACnD,CAAC,MAAM,IAAI1C,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE2C,WAAW,EAAE;MACvCF,SAAS,GAAGzC,eAAe,CAAC2C,WAAW;IACzC,CAAC,MAAM,IAAI3C,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE4C,IAAI,EAAE;MAChCH,SAAS,GAAGzC,eAAe,CAAC4C,IAAI;IAClC,CAAC,MAAM,IAAI5C,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE6C,UAAU,EAAE;MACtCJ,SAAS,GAAGzC,eAAe,CAAC6C,UAAU;IACxC;IAEA,MAAMmH,SAAS,GAAG,CAChB;MACEX,GAAG,EAAE,sBAAsB;MAC3BY,KAAK,EAAE,8BAA8B;MACrCxI,GAAG,EAAEvB,mBAAmB;MACxBqI,OAAO,EAAE,IAAI;MAAE;MACf2B,OAAO,EAAEA,CAAA,KAAM;QAAA,IAAAC,UAAA,EAAAC,sBAAA;QACb,MAAMnH,wBAAwB,GAAG,EAAAkH,UAAA,GAAA1H,SAAS,cAAA0H,UAAA,wBAAAC,sBAAA,GAATD,UAAA,CAAWvJ,sBAAsB,cAAAwJ,sBAAA,uBAAjCA,sBAAA,CAAmCrH,GAAG,CAACC,IAAI,IAAI;UAC9E,MAAME,KAAK,GAAGF,IAAI,CAACG,GAAG,IAAIH,IAAI,CAACI,sBAAsB,IAAIJ,IAAI,CAACK,oBAAoB,IAAI,CAAC;UACvF,OAAOC,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;QAC/B,CAAC,CAAC,KAAI,EAAE;QACR,OAAOD,wBAAwB,CAAClC,MAAM,GAAG,CAAC,IAAIkC,wBAAwB,CAACqG,IAAI,CAAChE,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;MAC7F;IACF,CAAC,EACD;MACE+D,GAAG,EAAE,yBAAyB;MAC9BY,KAAK,EAAE,gCAAgC;MACvCxI,GAAG,EAAEtB,sBAAsB;MAC3BoI,OAAO,EAAE,IAAI;MACb2B,OAAO,EAAEA,CAAA,KAAM;QAAA,IAAAG,WAAA,EAAAC,qBAAA;QACb,MAAM/G,2BAA2B,GAAG,EAAA8G,WAAA,GAAA5H,SAAS,cAAA4H,WAAA,wBAAAC,qBAAA,GAATD,WAAA,CAAWpJ,sBAAsB,cAAAqJ,qBAAA,uBAAjCA,qBAAA,CAAmCvH,GAAG,CAACC,IAAI,IAAI;UACjF,MAAME,KAAK,GAAGF,IAAI,CAACQ,GAAG,IAAIR,IAAI,CAACS,yBAAyB,IAAIT,IAAI,CAACU,uBAAuB,IAAI,CAAC;UAC7F,OAAOJ,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;QAC/B,CAAC,CAAC,KAAI,EAAE;QACR,OAAOK,2BAA2B,CAACxC,MAAM,GAAG,CAAC,IAAIwC,2BAA2B,CAAC+F,IAAI,CAAChE,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;MACnG,CAAC;MACDiF,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;QACXP,KAAK,EAAE,2CAA2C;QAClDQ,OAAO,EAAE;MACX;IACF,CAAC,EACD;MACEpB,GAAG,EAAE,0BAA0B;MAC/BY,KAAK,EAAE,4BAA4B;MACnCxI,GAAG,EAAErB,uBAAuB;MAC5BmI,OAAO,EAAE,IAAI;MACb2B,OAAO,EAAEA,CAAA,KAAM;QAAA,IAAAQ,WAAA,EAAAC,qBAAA;QACb,MAAMhH,4BAA4B,GAAG,EAAA+G,WAAA,GAAAjI,SAAS,cAAAiI,WAAA,wBAAAC,qBAAA,GAATD,WAAA,CAAWvJ,wBAAwB,cAAAwJ,qBAAA,uBAAnCA,qBAAA,CAAqC5H,GAAG,CAACC,IAAI,IAAI;UACpF,MAAME,KAAK,GAAGF,IAAI,CAACY,GAAG,IAAIZ,IAAI,CAACa,0BAA0B,IAAIb,IAAI,CAAC7B,wBAAwB,IAAI,CAAC;UAC/F,OAAOmC,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;QAC/B,CAAC,CAAC,KAAI,EAAE;QACR,OAAOS,4BAA4B,CAAC5C,MAAM,GAAG,CAAC,IAAI4C,4BAA4B,CAAC2F,IAAI,CAAChE,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;MACrG;MACA;IACF,CAAC,EACD;MACE+D,GAAG,EAAE,qBAAqB;MAC1BY,KAAK,EAAE,uBAAuB;MAC9BxI,GAAG,EAAEpB,iBAAiB;MACtBkI,OAAO,EAAE,IAAI;MACb2B,OAAO,EAAEA,CAAA,KAAM;QAAA,IAAAU,WAAA,EAAAC,qBAAA;QACb,MAAM/G,uBAAuB,GAAG,EAAA8G,WAAA,GAAAnI,SAAS,cAAAmI,WAAA,wBAAAC,qBAAA,GAATD,WAAA,CAAWvJ,mBAAmB,cAAAwJ,qBAAA,uBAA9BA,qBAAA,CAAgC9H,GAAG,CAACC,IAAI,IAAI;UAC1E,MAAME,KAAK,GAAGI,UAAU,CAACN,IAAI,CAACe,GAAG,IAAIf,IAAI,CAACgB,GAAG,CAAC;UAC9C,OAAOC,KAAK,CAACf,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,GAAG,CAAC,GAAGA,KAAK;QACnD,CAAC,CAAC,KAAI,EAAE;QACR,OAAOY,uBAAuB,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,uBAAuB,CAACwF,IAAI,CAAChE,GAAG,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,KAAK,CAAC,CAAC;MAC5G,CAAC;MACDiF,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;QACXP,KAAK,EAAE,6BAA6B;QACpCQ,OAAO,EAAE;MACX;IACF,CAAC,EACD;MACEpB,GAAG,EAAE,oBAAoB;MACzBY,KAAK,EAAE,sBAAsB;MAC7BxI,GAAG,EAAEnB,qBAAqB;MAC1BiI,OAAO,EAAE,IAAI;MACb2B,OAAO,EAAEA,CAAA,KAAM;QAAA,IAAAY,WAAA,EAAAC,qBAAA;QACb,MAAM7G,sBAAsB,GAAG,EAAA4G,WAAA,GAAArI,SAAS,cAAAqI,WAAA,wBAAAC,qBAAA,GAATD,WAAA,CAAWvJ,kBAAkB,cAAAwJ,qBAAA,uBAA7BA,qBAAA,CAA+BhI,GAAG,CAACC,IAAI,IACpEM,UAAU,CAACN,IAAI,CAACmB,GAAG,CAAC,IAAI,CAC1B,CAAC,KAAI,EAAE;QACP,OAAOD,sBAAsB,CAACnD,MAAM,GAAG,CAAC,IAAImD,sBAAsB,CAACoF,IAAI,CAAChE,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;MACzF,CAAC;MACDiF,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;QACXP,KAAK,EAAE,4BAA4B;QACnCQ,OAAO,EAAE;MACX;IACF,CAAC,CACF;;IAED;IACA,OAAOT,SAAS,CAACgB,MAAM,CAACpG,KAAK,IAC3BrE,kBAAkB,CAACqE,KAAK,CAACyE,GAAG,CAAC,IAAIzE,KAAK,CAACsF,OAAO,CAAC,CACjD,CAAC;EACH,CAAC;;EAED;EACA,MAAMf,aAAa,GAAGC,gBAAgB,CAAC,CAAC;;EAExC;EACA;EACA,MAAM6B,cAAc,GAAG9B,aAAa,CAACjH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChD,MAAMgJ,cAAc,GAAG/B,aAAa,CAACjH,KAAK,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAMiJ,WAAW,GAAIvG,KAAK,iBACxBlF,OAAA;IAAqB0L,SAAS,EAAE,2CAA2CxG,KAAK,CAACyG,qBAAqB,IAAI,EAAE,EAAG;IAAAC,QAAA,gBAC7G5L,OAAA;MACE0L,SAAS,EAAC,2CAA2C;MACrD3F,KAAK,EAAE5F,mBAAoB;MAAAyL,QAAA,EAE1B1G,KAAK,CAACqF;IAAK;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACNhM,OAAA;MAAK+B,GAAG,EAAEmD,KAAK,CAACnD,GAAI;MAAC2J,SAAS,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC3C9G,KAAK,CAAC2F,cAAc,iBACnB7K,OAAA;MAAK0L,SAAS,EAAC,4DAA4D;MAAAE,QAAA,gBACzE5L,OAAA;QACE0L,SAAS,EAAC,6BAA6B;QACvC3F,KAAK,EAAE;UAAE,GAAG5F,mBAAmB;UAAE8F,UAAU,EAAE;QAAU,CAAE;QAAA2F,QAAA,EAExD1G,KAAK,CAAC4F,WAAW,CAACP;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACNhM,OAAA;QAAK+F,KAAK,EAAE3F,gBAAiB;QAAAwL,QAAA,EAC1B1G,KAAK,CAAC4F,WAAW,CAACC;MAAO;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,GApBO9G,KAAK,CAACyE,GAAG;IAAAkC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAqBd,CACN;EAED,oBACEhM,OAAA;IAAK0L,SAAS,EAAC,KAAK;IAAAE,QAAA,gBAClB5L,OAAA;MAAK0L,SAAS,EAAC,oEAAoE;MAAAE,QAAA,gBAEjF5L,OAAA;QAAK0L,SAAS,EAAC,2FAA2F;QAAAE,QAAA,gBACxG5L,OAAA;UACE0L,SAAS,EAAC,sCAAsC;UAChD3F,KAAK,EAAE7F,eAAgB;UAAA0L,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhM,OAAA;UAAG0L,SAAS,EAAC,2BAA2B;UAAC3F,KAAK,EAAEkE,iBAAiB,CAAC,CAAE;UAAA2B,QAAA,GACjE/B,kBAAkB,CAACvJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2L,WAAW,EAAE3L,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4L,YAAY,CAAC,EAAC,KAAG,EAAC/B,iBAAiB,CAAC7J,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8J,WAAW,CAAC;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLT,cAAc,CAAClI,GAAG,CAAC6B,KAAK,IAAIuG,WAAW,CAACvG,KAAK,CAAC,CAAC;IAAA;MAAA2G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE7C,CAAC,EAGLR,cAAc,CAACnK,MAAM,GAAG,CAAC,iBACxBrB,OAAA;MAAK0L,SAAS,EAAC,gEAAgE;MAAAE,QAAA,gBAE7E5L,OAAA;QAAK0L,SAAS,EAAC,oGAAoG;QAAAE,QAAA,gBACjH5L,OAAA;UACE0L,SAAS,EAAC,sCAAsC;UAChD3F,KAAK,EAAE7F,eAAgB;UAAA0L,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhM,OAAA;UAAG0L,SAAS,EAAC,2BAA2B;UAAC3F,KAAK,EAAEkE,iBAAiB,CAAC,CAAE;UAAA2B,QAAA,GACjE/B,kBAAkB,CAACvJ,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2L,WAAW,EAAE3L,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4L,YAAY,CAAC,EAAC,KAAG,EAAC/B,iBAAiB,CAAC7J,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8J,WAAW,CAAC;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLR,cAAc,CAACnI,GAAG,CAAC6B,KAAK,IAAIuG,WAAW,CAACvG,KAAK,CAAC,CAAC;IAAA;MAAA2G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzL,EAAA,CA/1BIN,8BAA8B;AAAAkM,EAAA,GAA9BlM,8BAA8B;AAi2BpC,eAAeA,8BAA8B;AAAC,IAAAkM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}