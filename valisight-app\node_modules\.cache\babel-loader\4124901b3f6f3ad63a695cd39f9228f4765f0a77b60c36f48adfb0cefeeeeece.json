{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\Companies\\\\Components\\\\ReportSettings.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Switch, Card, Stack, Divider, TextField, Chip, IconButton, Tooltip, Button, useTheme, alpha, Snackbar, Alert, FormControl, Select, MenuItem, InputLabel, CircularProgress } from '@mui/material';\nimport { TuneOutlined as SettingsIcon, InfoOutlined as InfoIcon, CheckCircleOutlined as CheckIcon, SaveOutlined as SaveIcon, ScheduleOutlined as ComingSoonIcon } from '@mui/icons-material';\nimport { getContentSettingsByReportType, updateContentSettings } from '../../../services/contentSettings';\n\n// Fallback component for unsupported report types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComingSoonFallback = ({\n  reportType,\n  reportTypeOptions\n}) => {\n  _s();\n  var _reportTypeOptions$fi;\n  const theme = useTheme();\n\n  // Find the label for the selected report type\n  const selectedReportLabel = ((_reportTypeOptions$fi = reportTypeOptions.find(option => option.value === reportType)) === null || _reportTypeOptions$fi === void 0 ? void 0 : _reportTypeOptions$fi.label) || reportType;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 0,\n      sx: {\n        mb: 3,\n        p: 4,\n        backgroundColor: alpha('#1976d2', 0.05),\n        border: `1px solid ${alpha('#1976d2', 0.2)}`,\n        borderRadius: 2,\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(ComingSoonIcon, {\n          sx: {\n            color: '#1976d2',\n            fontSize: '3rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              color: '#1976d2',\n              fontWeight: 600,\n              fontSize: '1.3rem',\n              mb: 1\n            },\n            children: [selectedReportLabel, \" - Coming Soon!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              color: 'text.secondary',\n              fontSize: '1rem',\n              maxWidth: '500px',\n              margin: '0 auto'\n            },\n            children: \"This report type will be implemented soon.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(ComingSoonFallback, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = ComingSoonFallback;\nconst ReportSettings = ({\n  companyId\n}) => {\n  _s2();\n  const theme = useTheme();\n  const [settings, setSettings] = useState({\n    reportType: 'DEEPSIGHT',\n    // Flat structure - all chart settings at the same level\n    incomeSummary: true,\n    netIncome: true,\n    grossProfitMargin: true,\n    netProfitMargin: true,\n    roaAndRoe: true,\n    expensesTopAccounts: true,\n    expensesTopAccountsMonthly: true,\n    expensesWagesVsRevenueMonthly: true,\n    daysSalesOutstanding: true,\n    daysPayablesOutstanding: true,\n    daysInventoryOutstanding: true,\n    cashConversionCycle: true,\n    fixedAssetTurnover: true,\n    netChangeInCash: true,\n    quickRatio: true,\n    monthsCashOnHand: true,\n    thirteenMonthTrailing: true,\n    monthly: true,\n    ytd: true,\n    balanceSheet: true,\n    prompt: \"\"\n  });\n  const [isSaving, setIsSaving] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showSuccess, setShowSuccess] = useState(false);\n  const [showError, setShowError] = useState(false);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [hasChanges, setHasChanges] = useState(false);\n\n  // Report type options\n  const reportTypeOptions = [{\n    value: 'DEEPSIGHT',\n    label: 'Deepsight'\n  }, {\n    value: \"profitpulse\",\n    label: 'ProfitPulse (Monthly)'\n  }, {\n    value: \"kpitrack\",\n    label: 'KPITrack (Benchmark)'\n  }, {\n    value: \"gaap\",\n    label: 'GAAP Align'\n  }, {\n    value: \"fincheck\",\n    label: 'FinCheck (Current State)'\n  }, {\n    value: \"flowcast\",\n    label: 'FlowCast (13 Week)'\n  }];\n\n  // Settings sections configuration (for UI grouping only)\n  const settingsSections = [{\n    key: 'currentFiscalYear',\n    title: 'Current Fiscal Year',\n    description: 'Key performance metrics for the current fiscal period',\n    options: [{\n      key: 'incomeSummary',\n      label: 'Monthly Performance Breakdown (Income Summary)',\n      tooltip: 'Provides a monthly breakdown of income performance showing revenue trends and patterns throughout the fiscal year.'\n    }, {\n      key: 'netIncome',\n      label: 'Net Income/(Loss)',\n      tooltip: 'Shows the companys bottom-line profit or loss after deducting all expenses, taxes, and interest for the current fiscal year.'\n    }, {\n      key: 'grossProfitMargin',\n      label: 'Gross Profit Margin',\n      tooltip: 'Displays the percentage of revenue remaining after accounting for the cost of goods sold (COGS). Useful for measuring core business profitability.'\n    }, {\n      key: 'netProfitMargin',\n      label: 'Net Profit Margin',\n      tooltip: 'Shows the percentage of revenue converted into net profit after all expenses. Reflects overall efficiency in turning sales into profit.'\n    }]\n  }, {\n    key: 'expenseSummary',\n    title: 'Expense Summary',\n    description: 'Detailed analysis of company expenses and efficiency ratios',\n    options: [{\n      key: 'roaAndRoe',\n      label: 'Return on Assets and Equity',\n      tooltip: 'Return on Assets (ROA) measures how efficiently assets generate profit. Return on Equity (ROE) shows how effectively shareholder equity is used to deliver returns.'\n    }, {\n      key: 'expensesTopAccounts',\n      label: 'Expenses: Top Accounts',\n      tooltip: 'Identifies and analyzes the highest expense categories, helping prioritize cost management efforts.'\n    }, {\n      key: 'expensesTopAccountsMonthly',\n      label: 'Expenses: Top Accounts Monthly',\n      tooltip: 'Monthly breakdown of top expense accounts showing spending patterns and trends over time.'\n    }, {\n      key: 'expensesWagesVsRevenueMonthly',\n      label: 'Expenses: Wages Vs Revenue Monthly',\n      tooltip: 'Compares monthly wage expenses against revenue to track labor cost efficiency and productivity trends.'\n    }]\n  }, {\n    key: 'operationalEfficiency',\n    title: 'Operational Efficiency',\n    description: 'Key operational metrics measuring business efficiency',\n    options: [{\n      key: 'daysSalesOutstanding',\n      label: 'Days Sales (A/R) Outstanding',\n      tooltip: 'Measures the average number of days it takes to collect receivables. Lower values indicate faster cash collection.'\n    }, {\n      key: 'daysPayablesOutstanding',\n      label: 'Days Payables (AP) Outstanding',\n      tooltip: 'Shows the average number of days the company takes to pay suppliers. Helps assess payment timing and cash flow management.'\n    }, {\n      key: 'daysInventoryOutstanding',\n      label: 'Days Inventory Outstanding',\n      tooltip: 'Measures how many days of inventory the company holds on average. Lower values may indicate efficient inventory management.'\n    }, {\n      key: 'cashConversionCycle',\n      label: 'Cash Conversion Cycle',\n      tooltip: 'The time it takes to convert inventory investments into cash flows from sales. Shorter cycles indicate better working capital management.'\n    }, {\n      key: 'fixedAssetTurnover',\n      label: 'Fixed Asset Turnover',\n      tooltip: 'Measures how efficiently the company uses its fixed assets to generate sales. Higher ratios indicate better asset utilization.'\n    }]\n  }, {\n    key: 'liquiditySummary',\n    title: 'Liquidity Summary',\n    description: 'Cash flow and liquidity position analysis',\n    options: [{\n      key: 'netChangeInCash',\n      label: 'Net Change in Cash',\n      tooltip: 'Shows the overall increase or decrease in cash position over the period, indicating cash flow health.'\n    }, {\n      key: 'quickRatio',\n      label: 'Quick Ratio',\n      tooltip: 'Measures the company\\'s ability to pay short-term debts using the most liquid assets. Higher ratios indicate better liquidity.'\n    }, {\n      key: 'monthsCashOnHand',\n      label: 'Months Cash on Hand',\n      tooltip: 'Indicates how many months the company can operate with current cash reserves, providing insight into financial runway.'\n    }]\n  }, {\n    key: 'profitAndLoss',\n    title: 'Profit and Loss',\n    description: 'Comprehensive profit and loss statement analysis',\n    options: [{\n      key: 'thirteenMonthTrailing',\n      label: '13 Month Trailing',\n      tooltip: 'Provides a 13-month trailing view of profit and loss, smoothing seasonal variations and showing longer-term trends.'\n    }, {\n      key: 'monthly',\n      label: 'Monthly',\n      tooltip: 'Monthly profit and loss breakdown showing month-to-month performance and identifying seasonal patterns.'\n    }, {\n      key: 'ytd',\n      label: 'YTD',\n      tooltip: 'Year-to-date profit and loss summary comparing current performance against the full fiscal year.'\n    }]\n  }, {\n    key: 'balanceSheet',\n    title: 'Balance Sheet',\n    description: 'Complete balance sheet analysis and position summary',\n    options: [{\n      key: 'balanceSheet',\n      label: 'Balance Sheet',\n      tooltip: 'Comprehensive balance sheet showing assets, liabilities, and equity positions with period-over-period comparisons.'\n    }]\n  }];\n\n  // Check if the current report type is supported\n  const isCurrentReportTypeSupported = settings.reportType === 'DEEPSIGHT';\n\n  // Load existing settings on component mount\n  useEffect(() => {\n    const loadSettings = async () => {\n      if (!companyId) return;\n      try {\n        setIsLoading(true);\n        const response = await getContentSettingsByReportType(companyId, 'DEEPSIGHT');\n        if (response.data.success && response.data.data) {\n          const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\n          if (data && data.chartSettings) {\n            // Map the flat structure directly\n            setSettings(prev => ({\n              ...prev,\n              ...data.chartSettings,\n              prompt: data.promptDescription || prev.prompt\n            }));\n          }\n        }\n      } catch (error) {\n        setErrorMessage('Failed to load settings');\n        setShowError(true);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    loadSettings();\n  }, [companyId]);\n  const handleSwitchChange = field => event => {\n    setSettings(prev => ({\n      ...prev,\n      [field]: event.target.checked\n    }));\n    setHasChanges(true);\n  };\n  const handlePromptChange = event => {\n    setSettings(prev => ({\n      ...prev,\n      prompt: event.target.value\n    }));\n    setHasChanges(true);\n  };\n\n  // Updated handler for report type change\n  const handleReportTypeChange = async event => {\n    const newReportType = event.target.value;\n    setSettings(prev => ({\n      ...prev,\n      reportType: newReportType\n    }));\n\n    // If it's not DEEPSIGHT, don't try to load data - just show fallback\n    if (newReportType !== 'DEEPSIGHT') {\n      setHasChanges(false);\n      return;\n    }\n\n    // Load settings for DEEPSIGHT\n    if (!companyId) return;\n    try {\n      setIsLoading(true);\n      const response = await getContentSettingsByReportType(companyId, newReportType);\n      if (response.data.success && response.data.data) {\n        const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\n        if (data && data.chartSettings) {\n          setSettings(prev => ({\n            ...prev,\n            ...data.chartSettings,\n            prompt: data.promptDescription || ''\n          }));\n        }\n      }\n      setHasChanges(false);\n    } catch (error) {\n      setErrorMessage('Failed to load settings');\n      setShowError(true);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSave = async () => {\n    if (!companyId || !isCurrentReportTypeSupported) return;\n    setIsSaving(true);\n    try {\n      // Extract all chart settings (exclude reportType and prompt)\n      const {\n        reportType,\n        prompt,\n        ...chartSettings\n      } = settings;\n      const payload = {\n        chartSettings,\n        // This is now a flat object with all chart settings\n        promptDescription: prompt\n      };\n      await updateContentSettings(companyId, settings.reportType, payload);\n      setHasChanges(false);\n      setShowSuccess(true);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setErrorMessage(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to save settings');\n      setShowError(true);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleCloseSuccess = () => {\n    setShowSuccess(false);\n  };\n  const handleCloseError = () => {\n    setShowError(false);\n    setErrorMessage('');\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minWidth: '300px',\n          flexShrink: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(FormControl, {\n          size: \"small\",\n          sx: {\n            width: '350px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            id: \"report-type-label\",\n            sx: {\n              fontSize: '1rem',\n              fontWeight: 500,\n              color: 'text.secondary'\n            },\n            children: \"Report Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            labelId: \"report-type-label\",\n            id: \"report-type-select\",\n            value: settings.reportType,\n            label: \"Report Type\",\n            onChange: handleReportTypeChange,\n            sx: {\n              '& .MuiSelect-select': {\n                fontSize: '0.875rem',\n                padding: '15px 10px',\n                fontWeight: 500\n              },\n              '& .MuiOutlinedInput-notchedOutline': {\n                borderColor: 'grey.400'\n              },\n              '&:hover .MuiOutlinedInput-notchedOutline': {\n                borderColor: '#1976d2'\n              },\n              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                borderColor: '#1976d2'\n              }\n            },\n            children: reportTypeOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: option.value,\n              sx: {\n                fontSize: '0.875rem',\n                fontWeight: 500\n              },\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }, this), !isCurrentReportTypeSupported ? /*#__PURE__*/_jsxDEV(ComingSoonFallback, {\n      reportType: settings.reportType,\n      reportTypeOptions: reportTypeOptions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        elevation: 0,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 600,\n              color: 'text.primary',\n              fontSize: '1rem',\n              mb: 1\n            },\n            children: \"Report Components\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: 'text.secondary',\n              mb: 3\n            },\n            children: \"Select which financial metrics to include in your analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'grid',\n              gridTemplateColumns: {\n                xs: '1fr',\n                md: '1fr 1fr 1fr'\n              },\n              gap: 4,\n              maxWidth: '100%'\n            },\n            children: settingsSections.map((section, sectionIndex) => /*#__PURE__*/_jsxDEV(Card, {\n              elevation: 0,\n              sx: {\n                p: 3,\n                border: `1px solid ${alpha('#e0e0e0', 0.8)}`,\n                borderRadius: 2,\n                height: '450px',\n                transition: 'all 0.2s ease-in-out',\n                '&:hover': {\n                  boxShadow: `0 2px 8px ${alpha('#1976d2', 0.1)}`,\n                  borderColor: alpha('#1976d2', 0.3)\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    color: 'text.primary',\n                    fontSize: '1.1rem',\n                    mb: 0.5\n                  },\n                  children: section.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'text.secondary',\n                    fontSize: '0.875rem'\n                  },\n                  children: section.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  // backgroundColor: alpha('#f5f5f5', 0.3),\n                  borderRadius: 1.5,\n                  p: 2\n                  // border: `1px solid ${alpha('#e0e0e0', 0.5)}`,\n                },\n                children: section.options.map((option, index) => /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    py: 1.5,\n                    borderBottom: index < section.options.length - 1 ? '1px solid' : 'none',\n                    borderBottomColor: alpha('#e0e0e0', 0.4)\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: option.tooltip,\n                    arrow: true,\n                    placement: \"top-start\",\n                    sx: {\n                      '& .MuiTooltip-tooltip': {\n                        fontSize: '0.875rem',\n                        maxWidth: '400px',\n                        lineHeight: 1.4\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        flex: 1,\n                        // cursor: 'help',\n                        '&:hover': {\n                          backgroundColor: alpha('#1976d2', 0.04),\n                          borderRadius: 1,\n                          padding: '6px 10px',\n                          margin: '-6px -10px'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body1\",\n                        sx: {\n                          fontWeight: 500,\n                          color: 'text.primary',\n                          fontSize: '0.9rem',\n                          lineHeight: 1.3\n                        },\n                        children: option.label\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 618,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 608,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                    checked: settings[option.key],\n                    onChange: handleSwitchChange(option.key),\n                    inputProps: {\n                      'aria-label': 'controlled'\n                    },\n                    sx: {\n                      ml: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 27\n                  }, this)]\n                }, option.key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 21\n              }, this)]\n            }, section.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        elevation: 0,\n        sx: {\n          mb: 3,\n          width: 600,\n          transition: 'all 0.2s ease-in-out',\n          '&:hover': {\n            borderColor: '#1976d2',\n            backgroundColor: alpha('#1976d2', 0.02)\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          component: \"fieldset\",\n          sx: {\n            border: '2px solid',\n            borderColor: 'grey.400',\n            borderRadius: 1,\n            margin: 0,\n            position: 'relative',\n            backgroundColor: 'white',\n            transition: 'border-color 0.2s ease-in-out',\n            '&:focus-within': {\n              borderColor: '#1976d2'\n            },\n            '&:focus-within legend': {\n              color: '#1976d2'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            component: \"legend\",\n            sx: {\n              fontSize: '0.875rem',\n              fontWeight: 500,\n              fontFamily: '\"Roboto\",\"Helvetica\",\"Arial\",sans-serif',\n              color: 'text.secondary',\n              padding: '0 2px',\n              marginLeft: 1,\n              transition: 'color 0.2s ease-in-out'\n            },\n            children: \"Prompt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            multiline: true,\n            rows: 5,\n            value: settings.prompt,\n            onChange: handlePromptChange,\n            placeholder: \"Sample: Summarize financial performance for the current fiscal year, covering expenses, operational efficiency, liquidity, and profit & loss (monthly, YTD, and 13-month trailing). Include a balance sheet review, cost and COGS analysis, and conclude with executive insights, key focus areas, and recommendations for 2025\",\n            fullWidth: true,\n            variant: \"outlined\",\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                '& fieldset': {\n                  border: 'none'\n                },\n                '&:hover fieldset': {\n                  border: 'none'\n                },\n                '&.Mui-focused fieldset': {\n                  border: 'none'\n                }\n              },\n              '& .MuiInputBase-input': {\n                fontSize: '0.875rem',\n                lineHeight: 1.6,\n                '&::placeholder': {\n                  color: 'text.secondary',\n                  opacity: 0.7\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4,\n          display: 'flex',\n          justifyContent: 'flex-start'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"medium\",\n          onClick: handleSave,\n          disabled: !hasChanges || isSaving,\n          sx: {\n            textTransform: 'none',\n            minWidth: '120px',\n            px: 3\n          },\n          children: isSaving ? 'SAVING...' : 'SAVE'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showSuccess,\n      autoHideDuration: 4000,\n      onClose: handleCloseSuccess,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSuccess,\n        severity: \"success\",\n        variant: \"filled\",\n        sx: {\n          backgroundColor: '#1976d2',\n          '& .MuiAlert-icon': {\n            color: 'white'\n          }\n        },\n        children: \"Settings saved successfully!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 752,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showError,\n      autoHideDuration: 6000,\n      onClose: handleCloseError,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseError,\n        severity: \"error\",\n        variant: \"filled\",\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 768,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 444,\n    columnNumber: 5\n  }, this);\n};\n_s2(ReportSettings, \"a1EsUrGcyfMbkvorHbaL188GWQg=\", false, function () {\n  return [useTheme];\n});\n_c2 = ReportSettings;\nexport default ReportSettings;\nvar _c, _c2;\n$RefreshReg$(_c, \"ComingSoonFallback\");\n$RefreshReg$(_c2, \"ReportSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Switch", "Card", "<PERSON><PERSON>", "Divider", "TextField", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "useTheme", "alpha", "Snackbar", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "InputLabel", "CircularProgress", "TuneOutlined", "SettingsIcon", "InfoOutlined", "InfoIcon", "CheckCircleOutlined", "CheckIcon", "SaveOutlined", "SaveIcon", "ScheduleOutlined", "ComingSoonIcon", "getContentSettingsByReportType", "updateContentSettings", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComingSoon<PERSON>allback", "reportType", "reportTypeOptions", "_s", "_reportTypeOptions$fi", "theme", "selectedReport<PERSON><PERSON>l", "find", "option", "value", "label", "sx", "width", "children", "elevation", "mb", "p", "backgroundColor", "border", "borderRadius", "textAlign", "display", "flexDirection", "alignItems", "gap", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "max<PERSON><PERSON><PERSON>", "margin", "_c", "ReportSettings", "companyId", "_s2", "settings", "setSettings", "incomeSummary", "netIncome", "grossProfitMargin", "netProfitMargin", "roaAndRoe", "expensesTopAccounts", "expensesTopAccountsMonthly", "expensesWagesVsRevenueMonthly", "daysSalesOutstanding", "daysPayablesOutstanding", "daysInventoryOutstanding", "cashConversionCycle", "fixedAssetTurnover", "netChangeInCash", "quickRatio", "monthsCashOnHand", "thirteenMonthTrailing", "monthly", "ytd", "balanceSheet", "prompt", "isSaving", "setIsSaving", "isLoading", "setIsLoading", "showSuccess", "setShowSuccess", "showError", "setShowError", "errorMessage", "setErrorMessage", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "settingsSections", "key", "title", "description", "options", "tooltip", "isCurrentReportTypeSupported", "loadSettings", "response", "data", "success", "Array", "isArray", "chartSettings", "prev", "promptDescription", "error", "handleSwitchChange", "field", "event", "target", "checked", "handlePromptChange", "handleReportTypeChange", "newReportType", "handleSave", "payload", "_error$response", "_error$response$data", "message", "handleCloseSuccess", "handleCloseError", "justifyContent", "minHeight", "min<PERSON><PERSON><PERSON>", "flexShrink", "size", "id", "labelId", "onChange", "padding", "borderColor", "map", "gridTemplateColumns", "xs", "md", "section", "sectionIndex", "height", "transition", "boxShadow", "index", "py", "borderBottom", "length", "borderBottomColor", "arrow", "placement", "lineHeight", "flex", "inputProps", "ml", "component", "position", "fontFamily", "marginLeft", "multiline", "rows", "placeholder", "fullWidth", "opacity", "mt", "onClick", "disabled", "textTransform", "px", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/Companies/Components/ReportSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  <PERSON>,\r\n  Typo<PERSON>,\r\n  Switch,\r\n  Card,\r\n  Stack,\r\n  Divider,\r\n  TextField,\r\n  Chip,\r\n  IconButton,\r\n  Tooltip,\r\n  Button,\r\n  useTheme,\r\n  alpha,\r\n  Snackbar,\r\n  Alert,\r\n  FormControl,\r\n  Select,\r\n  MenuItem,\r\n  InputLabel,\r\n  CircularProgress,\r\n} from '@mui/material';\r\n\r\nimport {\r\n  TuneOutlined as SettingsIcon,\r\n  InfoOutlined as InfoIcon,\r\n  CheckCircleOutlined as CheckIcon,\r\n  SaveOutlined as SaveIcon,\r\n  ScheduleOutlined as ComingSoonIcon,\r\n} from '@mui/icons-material';\r\n\r\nimport {\r\n  getContentSettingsByReportType,\r\n  updateContentSettings\r\n} from '../../../services/contentSettings';\r\n\r\n// Fallback component for unsupported report types\r\nconst ComingSoonFallback = ({ reportType, reportTypeOptions }) => {\r\n  const theme = useTheme();\r\n  \r\n  // Find the label for the selected report type\r\n  const selectedReportLabel = reportTypeOptions.find(\r\n    option => option.value === reportType\r\n  )?.label || reportType;\r\n\r\n  return (\r\n    <Box sx={{ width: '100%' }}>\r\n      {/* Coming Soon Banner */}\r\n      <Card\r\n        elevation={0}\r\n        sx={{\r\n          mb: 3,\r\n          p: 4,\r\n          backgroundColor: alpha('#1976d2', 0.05),\r\n          border: `1px solid ${alpha('#1976d2', 0.2)}`,\r\n          borderRadius: 2,\r\n          textAlign: 'center',\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>\r\n          <ComingSoonIcon sx={{ color: '#1976d2', fontSize: '3rem' }} />\r\n          <Box>\r\n            <Typography\r\n              variant=\"h5\"\r\n              sx={{\r\n                color: '#1976d2',\r\n                fontWeight: 600,\r\n                fontSize: '1.3rem',\r\n                mb: 1,\r\n              }}\r\n            >\r\n              {selectedReportLabel} - Coming Soon!\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body1\"\r\n              sx={{\r\n                color: 'text.secondary',\r\n                fontSize: '1rem',\r\n                maxWidth: '500px',\r\n                margin: '0 auto',\r\n              }}\r\n            >\r\n              This report type will be implemented soon.\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n      </Card>\r\n    </Box>\r\n  );\r\n};\r\n\r\nconst ReportSettings = ({ companyId }) => {\r\n  const theme = useTheme();\r\n  const [settings, setSettings] = useState({\r\n    reportType: 'DEEPSIGHT',\r\n    // Flat structure - all chart settings at the same level\r\n    incomeSummary: true,\r\n    netIncome: true,\r\n    grossProfitMargin: true,\r\n    netProfitMargin: true,\r\n    roaAndRoe: true,\r\n    expensesTopAccounts: true,\r\n    expensesTopAccountsMonthly: true,\r\n    expensesWagesVsRevenueMonthly: true,\r\n    daysSalesOutstanding: true,\r\n    daysPayablesOutstanding: true,\r\n    daysInventoryOutstanding: true,\r\n    cashConversionCycle: true,\r\n    fixedAssetTurnover: true,\r\n    netChangeInCash: true,\r\n    quickRatio: true,\r\n    monthsCashOnHand: true,\r\n    thirteenMonthTrailing: true,\r\n    monthly: true,\r\n    ytd: true,\r\n    balanceSheet: true,\r\n    prompt: \"\",\r\n  });\r\n\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [showSuccess, setShowSuccess] = useState(false);\r\n  const [showError, setShowError] = useState(false);\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [hasChanges, setHasChanges] = useState(false);\r\n\r\n  // Report type options\r\n  const reportTypeOptions = [\r\n    {\r\n      value: 'DEEPSIGHT',\r\n      label: 'Deepsight'\r\n    },\r\n    {\r\n      value: \"profitpulse\",\r\n      label: 'ProfitPulse (Monthly)'\r\n    },\r\n    {\r\n      value: \"kpitrack\",\r\n      label: 'KPITrack (Benchmark)'\r\n    },\r\n    {\r\n      value: \"gaap\",\r\n      label: 'GAAP Align'\r\n    },\r\n    {\r\n      value: \"fincheck\",\r\n      label: 'FinCheck (Current State)'\r\n    },\r\n    {\r\n      value: \"flowcast\",\r\n      label: 'FlowCast (13 Week)'\r\n    }\r\n  ];\r\n\r\n  // Settings sections configuration (for UI grouping only)\r\n  const settingsSections = [\r\n    {\r\n      key: 'currentFiscalYear',\r\n      title: 'Current Fiscal Year',\r\n      description: 'Key performance metrics for the current fiscal period',\r\n      options: [\r\n        {\r\n          key: 'incomeSummary',\r\n          label: 'Monthly Performance Breakdown (Income Summary)',\r\n          tooltip: 'Provides a monthly breakdown of income performance showing revenue trends and patterns throughout the fiscal year.'\r\n        },\r\n        {\r\n          key: 'netIncome',\r\n          label: 'Net Income/(Loss)',\r\n          tooltip: 'Shows the companys bottom-line profit or loss after deducting all expenses, taxes, and interest for the current fiscal year.'\r\n        },\r\n        {\r\n          key: 'grossProfitMargin',\r\n          label: 'Gross Profit Margin',\r\n          tooltip: 'Displays the percentage of revenue remaining after accounting for the cost of goods sold (COGS). Useful for measuring core business profitability.'\r\n        },\r\n        {\r\n          key: 'netProfitMargin',\r\n          label: 'Net Profit Margin',\r\n          tooltip: 'Shows the percentage of revenue converted into net profit after all expenses. Reflects overall efficiency in turning sales into profit.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'expenseSummary',\r\n      title: 'Expense Summary',\r\n      description: 'Detailed analysis of company expenses and efficiency ratios',\r\n      options: [\r\n        {\r\n          key: 'roaAndRoe',\r\n          label: 'Return on Assets and Equity',\r\n          tooltip: 'Return on Assets (ROA) measures how efficiently assets generate profit. Return on Equity (ROE) shows how effectively shareholder equity is used to deliver returns.'\r\n        },\r\n        {\r\n          key: 'expensesTopAccounts',\r\n          label: 'Expenses: Top Accounts',\r\n          tooltip: 'Identifies and analyzes the highest expense categories, helping prioritize cost management efforts.'\r\n        },\r\n        {\r\n          key: 'expensesTopAccountsMonthly',\r\n          label: 'Expenses: Top Accounts Monthly',\r\n          tooltip: 'Monthly breakdown of top expense accounts showing spending patterns and trends over time.'\r\n        },\r\n        {\r\n          key: 'expensesWagesVsRevenueMonthly',\r\n          label: 'Expenses: Wages Vs Revenue Monthly',\r\n          tooltip: 'Compares monthly wage expenses against revenue to track labor cost efficiency and productivity trends.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'operationalEfficiency',\r\n      title: 'Operational Efficiency',\r\n      description: 'Key operational metrics measuring business efficiency',\r\n      options: [\r\n        {\r\n          key: 'daysSalesOutstanding',\r\n          label: 'Days Sales (A/R) Outstanding',\r\n          tooltip: 'Measures the average number of days it takes to collect receivables. Lower values indicate faster cash collection.'\r\n        },\r\n        {\r\n          key: 'daysPayablesOutstanding',\r\n          label: 'Days Payables (AP) Outstanding',\r\n          tooltip: 'Shows the average number of days the company takes to pay suppliers. Helps assess payment timing and cash flow management.'\r\n        },\r\n        {\r\n          key: 'daysInventoryOutstanding',\r\n          label: 'Days Inventory Outstanding',\r\n          tooltip: 'Measures how many days of inventory the company holds on average. Lower values may indicate efficient inventory management.'\r\n        },\r\n        {\r\n          key: 'cashConversionCycle',\r\n          label: 'Cash Conversion Cycle',\r\n          tooltip: 'The time it takes to convert inventory investments into cash flows from sales. Shorter cycles indicate better working capital management.'\r\n        },\r\n        {\r\n          key: 'fixedAssetTurnover',\r\n          label: 'Fixed Asset Turnover',\r\n          tooltip: 'Measures how efficiently the company uses its fixed assets to generate sales. Higher ratios indicate better asset utilization.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'liquiditySummary',\r\n      title: 'Liquidity Summary',\r\n      description: 'Cash flow and liquidity position analysis',\r\n      options: [\r\n        {\r\n          key: 'netChangeInCash',\r\n          label: 'Net Change in Cash',\r\n          tooltip: 'Shows the overall increase or decrease in cash position over the period, indicating cash flow health.'\r\n        },\r\n        {\r\n          key: 'quickRatio',\r\n          label: 'Quick Ratio',\r\n          tooltip: 'Measures the company\\'s ability to pay short-term debts using the most liquid assets. Higher ratios indicate better liquidity.'\r\n        },\r\n        {\r\n          key: 'monthsCashOnHand',\r\n          label: 'Months Cash on Hand',\r\n          tooltip: 'Indicates how many months the company can operate with current cash reserves, providing insight into financial runway.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'profitAndLoss',\r\n      title: 'Profit and Loss',\r\n      description: 'Comprehensive profit and loss statement analysis',\r\n      options: [\r\n        {\r\n          key: 'thirteenMonthTrailing',\r\n          label: '13 Month Trailing',\r\n          tooltip: 'Provides a 13-month trailing view of profit and loss, smoothing seasonal variations and showing longer-term trends.'\r\n        },\r\n        {\r\n          key: 'monthly',\r\n          label: 'Monthly',\r\n          tooltip: 'Monthly profit and loss breakdown showing month-to-month performance and identifying seasonal patterns.'\r\n        },\r\n        {\r\n          key: 'ytd',\r\n          label: 'YTD',\r\n          tooltip: 'Year-to-date profit and loss summary comparing current performance against the full fiscal year.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'balanceSheet',\r\n      title: 'Balance Sheet',\r\n      description: 'Complete balance sheet analysis and position summary',\r\n      options: [\r\n        {\r\n          key: 'balanceSheet',\r\n          label: 'Balance Sheet',\r\n          tooltip: 'Comprehensive balance sheet showing assets, liabilities, and equity positions with period-over-period comparisons.'\r\n        },\r\n      ]\r\n    },\r\n  ];\r\n\r\n  // Check if the current report type is supported\r\n  const isCurrentReportTypeSupported = settings.reportType === 'DEEPSIGHT';\r\n\r\n  // Load existing settings on component mount\r\n  useEffect(() => {\r\n    const loadSettings = async () => {\r\n      if (!companyId) return;\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        const response = await getContentSettingsByReportType(companyId, 'DEEPSIGHT');\r\n\r\n        if (response.data.success && response.data.data) {\r\n          const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\r\n          \r\n          if (data && data.chartSettings) {\r\n            // Map the flat structure directly\r\n            setSettings(prev => ({\r\n              ...prev,\r\n              ...data.chartSettings,\r\n              prompt: data.promptDescription || prev.prompt,\r\n            }));\r\n          }\r\n        }\r\n      } catch (error) {\r\n        setErrorMessage('Failed to load settings');\r\n        setShowError(true);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    loadSettings();\r\n  }, [companyId]);\r\n\r\n  const handleSwitchChange = (field) => (event) => {\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      [field]: event.target.checked\r\n    }));\r\n    setHasChanges(true);\r\n  };\r\n\r\n  const handlePromptChange = (event) => {\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      prompt: event.target.value\r\n    }));\r\n    setHasChanges(true);\r\n  };\r\n\r\n  // Updated handler for report type change\r\n  const handleReportTypeChange = async (event) => {\r\n    const newReportType = event.target.value;\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      reportType: newReportType\r\n    }));\r\n\r\n    // If it's not DEEPSIGHT, don't try to load data - just show fallback\r\n    if (newReportType !== 'DEEPSIGHT') {\r\n      setHasChanges(false);\r\n      return;\r\n    }\r\n\r\n    // Load settings for DEEPSIGHT\r\n    if (!companyId) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await getContentSettingsByReportType(companyId, newReportType);\r\n\r\n      if (response.data.success && response.data.data) {\r\n        const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\r\n        \r\n        if (data && data.chartSettings) {\r\n          setSettings(prev => ({\r\n            ...prev,\r\n            ...data.chartSettings,\r\n            prompt: data.promptDescription || '',\r\n          }));\r\n        }\r\n      }\r\n      setHasChanges(false);\r\n    } catch (error) {\r\n      setErrorMessage('Failed to load settings');\r\n      setShowError(true);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!companyId || !isCurrentReportTypeSupported) return;\r\n\r\n    setIsSaving(true);\r\n\r\n    try {\r\n      // Extract all chart settings (exclude reportType and prompt)\r\n      const { reportType, prompt, ...chartSettings } = settings;\r\n      \r\n      const payload = {\r\n        chartSettings, // This is now a flat object with all chart settings\r\n        promptDescription: prompt,\r\n      };\r\n\r\n      await updateContentSettings(companyId, settings.reportType, payload);\r\n\r\n      setHasChanges(false);\r\n      setShowSuccess(true);\r\n    } catch (error) {\r\n      setErrorMessage(error.response?.data?.message || 'Failed to save settings');\r\n      setShowError(true);\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCloseSuccess = () => {\r\n    setShowSuccess(false);\r\n  };\r\n\r\n  const handleCloseError = () => {\r\n    setShowError(false);\r\n    setErrorMessage('');\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Box sx={{\r\n        width: '100%',\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        minHeight: '400px'\r\n      }}>\r\n        <CircularProgress />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box sx={{ width: '100%' }}>\r\n      {/* Header Section with Report Type Dropdown */}\r\n      <Box sx={{ mb: 4 }}>\r\n        {/* Report Type Selection */}\r\n        <Box sx={{ minWidth: '300px', flexShrink: 0 }}>\r\n          <FormControl  size=\"small\"\r\n          sx={{width: '350px'}}>\r\n            <InputLabel \r\n              id=\"report-type-label\"\r\n              sx={{\r\n                fontSize: '1rem',\r\n                fontWeight: 500,\r\n                color: 'text.secondary',\r\n              }}\r\n            >\r\n              Report Type\r\n            </InputLabel>\r\n            <Select\r\n              labelId=\"report-type-label\"\r\n              id=\"report-type-select\"\r\n              value={settings.reportType}\r\n              label=\"Report Type\"\r\n              onChange={handleReportTypeChange}\r\n              sx={{\r\n                '& .MuiSelect-select': {\r\n                  fontSize: '0.875rem',\r\n                  padding: '15px 10px',\r\n                  fontWeight: 500,\r\n                },\r\n                '& .MuiOutlinedInput-notchedOutline': {\r\n                  borderColor: 'grey.400',\r\n                },\r\n                '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                  borderColor: '#1976d2',\r\n                },\r\n                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                  borderColor: '#1976d2',\r\n                },\r\n              }}\r\n            >\r\n              {reportTypeOptions.map((option) => (\r\n                <MenuItem \r\n                  key={option.value} \r\n                  value={option.value}\r\n                  sx={{\r\n                    fontSize: '0.875rem',\r\n                    fontWeight: 500,\r\n                  }}\r\n                >\r\n                  {option.label}\r\n                </MenuItem>\r\n              ))}\r\n            </Select>\r\n          </FormControl>\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Conditional Rendering: Show fallback for unsupported report types */}\r\n      {!isCurrentReportTypeSupported ? (\r\n        <ComingSoonFallback \r\n          reportType={settings.reportType} \r\n          reportTypeOptions={reportTypeOptions} \r\n        />\r\n      ) : (\r\n        <>\r\n          {/* Report Components Section - Only for supported types */}\r\n          <Card elevation={0}>\r\n            <Box sx={{ mb: 3 }}>\r\n              <Typography\r\n                variant=\"subtitle1\"\r\n                sx={{\r\n                  fontWeight: 600,\r\n                  color: 'text.primary',\r\n                  fontSize: '1rem',\r\n                  mb: 1,\r\n                }}\r\n              >\r\n                Report Components\r\n              </Typography>\r\n              <Typography\r\n                variant=\"body2\"\r\n                sx={{ color: 'text.secondary', mb: 3 }}\r\n              >\r\n                Select which financial metrics to include in your analysis\r\n              </Typography>\r\n\r\n              {/* Grid Layout for Sections */}\r\n              <Box sx={{ \r\n                display: 'grid', \r\n                gridTemplateColumns: { xs: '1fr', md: '1fr 1fr 1fr' }, \r\n                gap: 4,\r\n                maxWidth: '100%'\r\n              }}>\r\n                {settingsSections.map((section, sectionIndex) => (\r\n                  <Card\r\n                    key={section.key}\r\n                    elevation={0}\r\n                    sx={{\r\n                      p: 3,\r\n                      border: `1px solid ${alpha('#e0e0e0', 0.8)}`,\r\n                      borderRadius: 2,\r\n                      height: '450px',\r\n                      transition: 'all 0.2s ease-in-out',\r\n                      '&:hover': {\r\n                        boxShadow: `0 2px 8px ${alpha('#1976d2', 0.1)}`,\r\n                        borderColor: alpha('#1976d2', 0.3),\r\n                      },\r\n                    }}\r\n                  >\r\n                    {/* Section Header */}\r\n                    <Box sx={{ mb: 3 }}>\r\n                      <Typography\r\n                        variant=\"h6\"\r\n                        sx={{\r\n                          fontWeight: 600,\r\n                          color: 'text.primary',\r\n                          fontSize: '1.1rem',\r\n                          mb: 0.5,\r\n                        }}\r\n                      >\r\n                        {section.title}\r\n                      </Typography>\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{ \r\n                          color: 'text.secondary',\r\n                          fontSize: '0.875rem',\r\n                        }}\r\n                      >\r\n                        {section.description}\r\n                      </Typography>\r\n                    </Box>\r\n\r\n                    {/* Section Options */}\r\n                    <Box sx={{\r\n                      // backgroundColor: alpha('#f5f5f5', 0.3),\r\n                      borderRadius: 1.5,\r\n                      p: 2,\r\n                      // border: `1px solid ${alpha('#e0e0e0', 0.5)}`,\r\n                    }}>\r\n                      {section.options.map((option, index) => (\r\n                        <Box \r\n                          key={option.key}\r\n                          sx={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            justifyContent: 'space-between',\r\n                            py: 1.5,\r\n                            borderBottom: index < section.options.length - 1 ? '1px solid' : 'none',\r\n                            borderBottomColor: alpha('#e0e0e0', 0.4),\r\n                          }}\r\n                        >\r\n                          <Tooltip \r\n                            title={option.tooltip}\r\n                            arrow\r\n                            placement=\"top-start\"\r\n                            sx={{\r\n                              '& .MuiTooltip-tooltip': {\r\n                                fontSize: '0.875rem',\r\n                                maxWidth: '400px',\r\n                                lineHeight: 1.4,\r\n                              }\r\n                            }}\r\n                          >\r\n                            <Box sx={{ \r\n                              flex: 1,\r\n                              // cursor: 'help',\r\n                              '&:hover': {\r\n                                backgroundColor: alpha('#1976d2', 0.04),\r\n                                borderRadius: 1,\r\n                                padding: '6px 10px',\r\n                                margin: '-6px -10px',\r\n                              }\r\n                            }}>\r\n                              <Typography\r\n                                variant=\"body1\"\r\n                                sx={{\r\n                                  fontWeight: 500,\r\n                                  color: 'text.primary',\r\n                                  fontSize: '0.9rem',\r\n                                  lineHeight: 1.3,\r\n                                }}\r\n                              >\r\n                                {option.label}\r\n                              </Typography>\r\n                            </Box>\r\n                          </Tooltip>\r\n\r\n                          <Switch\r\n                            checked={settings[option.key]}\r\n                            onChange={handleSwitchChange(option.key)}\r\n                            inputProps={{ 'aria-label': 'controlled' }}\r\n                            sx={{ ml: 2 }}\r\n                          />\r\n                        </Box>\r\n                      ))}\r\n                    </Box>\r\n                  </Card>\r\n                ))}\r\n              </Box>\r\n            </Box>\r\n          </Card>\r\n\r\n          {/* Custom Analysis Prompt Section - Only for supported types */}\r\n          <Card\r\n            elevation={0}\r\n            sx={{\r\n              mb: 3,\r\n              width: 600,\r\n              transition: 'all 0.2s ease-in-out',\r\n              '&:hover': {\r\n                borderColor: '#1976d2',\r\n                backgroundColor: alpha('#1976d2', 0.02),\r\n              },\r\n            }}\r\n          >\r\n            <Box\r\n              component=\"fieldset\"\r\n              sx={{\r\n                border: '2px solid',\r\n                borderColor: 'grey.400',\r\n                borderRadius: 1,\r\n                margin: 0,\r\n                position: 'relative',\r\n                backgroundColor: 'white',\r\n                transition: 'border-color 0.2s ease-in-out',\r\n                '&:focus-within': {\r\n                  borderColor: '#1976d2',\r\n                },\r\n                '&:focus-within legend': {\r\n                  color: '#1976d2',\r\n                },\r\n              }}\r\n            >\r\n              <Box\r\n                component=\"legend\"\r\n                sx={{\r\n                  fontSize: '0.875rem',\r\n                  fontWeight: 500,\r\n                  fontFamily: '\"Roboto\",\"Helvetica\",\"Arial\",sans-serif',\r\n                  color: 'text.secondary',\r\n                  padding: '0 2px',\r\n                  marginLeft: 1,\r\n                  transition: 'color 0.2s ease-in-out',\r\n                }}\r\n              >\r\n                Prompt\r\n              </Box>\r\n              \r\n              <TextField\r\n                multiline\r\n                rows={5}\r\n                value={settings.prompt}\r\n                onChange={handlePromptChange}\r\n                placeholder=\"Sample: Summarize financial performance for the current fiscal year, covering expenses, operational efficiency, liquidity, and profit & loss (monthly, YTD, and 13-month trailing). Include a balance sheet review, cost and COGS analysis, and conclude with executive insights, key focus areas, and recommendations for 2025\"\r\n                fullWidth\r\n                variant=\"outlined\"\r\n                sx={{\r\n                  '& .MuiOutlinedInput-root': {\r\n                    '& fieldset': {\r\n                      border: 'none',\r\n                    },\r\n                    '&:hover fieldset': {\r\n                      border: 'none',\r\n                    },\r\n                    '&.Mui-focused fieldset': {\r\n                      border: 'none',\r\n                    },\r\n                  },\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: 1.6,\r\n                    '&::placeholder': {\r\n                      color: 'text.secondary',\r\n                      opacity: 0.7,\r\n                    },\r\n                  },\r\n                }}\r\n              />\r\n            </Box>\r\n          </Card>\r\n\r\n          {/* Save Button - Only for supported types */}\r\n          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-start' }}>\r\n            <Button\r\n              variant=\"contained\"\r\n              size=\"medium\"\r\n              onClick={handleSave}\r\n              disabled={!hasChanges || isSaving}\r\n              sx={{\r\n                textTransform: 'none',\r\n                minWidth: '120px',\r\n                px: 3,\r\n              }}\r\n            >\r\n              {isSaving ? 'SAVING...' : 'SAVE'}\r\n            </Button>\r\n          </Box>\r\n        </>\r\n      )}\r\n\r\n      {/* Success Snackbar */}\r\n      <Snackbar\r\n        open={showSuccess}\r\n        autoHideDuration={4000}\r\n        onClose={handleCloseSuccess}\r\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\r\n      >\r\n        <Alert\r\n          onClose={handleCloseSuccess}\r\n          severity=\"success\"\r\n          variant=\"filled\"\r\n          sx={{\r\n            backgroundColor: '#1976d2',\r\n            '& .MuiAlert-icon': {\r\n              color: 'white',\r\n            },\r\n          }}\r\n        >\r\n          Settings saved successfully!\r\n        </Alert>\r\n      </Snackbar>\r\n\r\n      {/* Error Snackbar */}\r\n      <Snackbar\r\n        open={showError}\r\n        autoHideDuration={6000}\r\n        onClose={handleCloseError}\r\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\r\n      >\r\n        <Alert\r\n          onClose={handleCloseError}\r\n          severity=\"error\"\r\n          variant=\"filled\"\r\n        >\r\n          {errorMessage}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ReportSettings;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,OAAO,EACPC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,gBAAgB,QACX,eAAe;AAEtB,SACEC,YAAY,IAAIC,YAAY,EAC5BC,YAAY,IAAIC,QAAQ,EACxBC,mBAAmB,IAAIC,SAAS,EAChCC,YAAY,IAAIC,QAAQ,EACxBC,gBAAgB,IAAIC,cAAc,QAC7B,qBAAqB;AAE5B,SACEC,8BAA8B,EAC9BC,qBAAqB,QAChB,mCAAmC;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAkB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChE,MAAMC,KAAK,GAAG9B,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAM+B,mBAAmB,GAAG,EAAAF,qBAAA,GAAAF,iBAAiB,CAACK,IAAI,CAChDC,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAKR,UAC7B,CAAC,cAAAG,qBAAA,uBAF2BA,qBAAA,CAEzBM,KAAK,KAAIT,UAAU;EAEtB,oBACEJ,OAAA,CAACjC,GAAG;IAAC+C,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAC,QAAA,eAEzBhB,OAAA,CAAC9B,IAAI;MACH+C,SAAS,EAAE,CAAE;MACbH,EAAE,EAAE;QACFI,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,eAAe,EAAEzC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC;QACvC0C,MAAM,EAAE,aAAa1C,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;QAC5C2C,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,eAEFhB,OAAA,CAACjC,GAAG;QAAC+C,EAAE,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAX,QAAA,gBAClFhB,OAAA,CAACJ,cAAc;UAACkB,EAAE,EAAE;YAAEc,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE;UAAO;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DjC,OAAA,CAACjC,GAAG;UAAAiD,QAAA,gBACFhB,OAAA,CAAChC,UAAU;YACTkE,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFc,KAAK,EAAE,SAAS;cAChBO,UAAU,EAAE,GAAG;cACfN,QAAQ,EAAE,QAAQ;cAClBX,EAAE,EAAE;YACN,CAAE;YAAAF,QAAA,GAEDP,mBAAmB,EAAC,iBACvB;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAAChC,UAAU;YACTkE,OAAO,EAAC,OAAO;YACfpB,EAAE,EAAE;cACFc,KAAK,EAAE,gBAAgB;cACvBC,QAAQ,EAAE,MAAM;cAChBO,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;YACV,CAAE;YAAArB,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3B,EAAA,CApDIH,kBAAkB;EAAA,QACRzB,QAAQ;AAAA;AAAA4D,EAAA,GADlBnC,kBAAkB;AAsDxB,MAAMoC,cAAc,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,GAAA;EACxC,MAAMjC,KAAK,GAAG9B,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC;IACvCuC,UAAU,EAAE,WAAW;IACvB;IACAwC,aAAa,EAAE,IAAI;IACnBC,SAAS,EAAE,IAAI;IACfC,iBAAiB,EAAE,IAAI;IACvBC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE,IAAI;IACfC,mBAAmB,EAAE,IAAI;IACzBC,0BAA0B,EAAE,IAAI;IAChCC,6BAA6B,EAAE,IAAI;IACnCC,oBAAoB,EAAE,IAAI;IAC1BC,uBAAuB,EAAE,IAAI;IAC7BC,wBAAwB,EAAE,IAAI;IAC9BC,mBAAmB,EAAE,IAAI;IACzBC,kBAAkB,EAAE,IAAI;IACxBC,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,IAAI;IACtBC,qBAAqB,EAAE,IAAI;IAC3BC,OAAO,EAAE,IAAI;IACbC,GAAG,EAAE,IAAI;IACTC,YAAY,EAAE,IAAI;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsG,SAAS,EAAEC,YAAY,CAAC,GAAGvG,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACwG,WAAW,EAAEC,cAAc,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0G,SAAS,EAAEC,YAAY,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4G,YAAY,EAAEC,eAAe,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8G,UAAU,EAAEC,aAAa,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAMwC,iBAAiB,GAAG,CACxB;IACEO,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMgE,gBAAgB,GAAG,CACvB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,uDAAuD;IACpEC,OAAO,EAAE,CACP;MACEH,GAAG,EAAE,eAAe;MACpBjE,KAAK,EAAE,gDAAgD;MACvDqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,WAAW;MAChBjE,KAAK,EAAE,mBAAmB;MAC1BqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,mBAAmB;MACxBjE,KAAK,EAAE,qBAAqB;MAC5BqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,iBAAiB;MACtBjE,KAAK,EAAE,mBAAmB;MAC1BqE,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEJ,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,6DAA6D;IAC1EC,OAAO,EAAE,CACP;MACEH,GAAG,EAAE,WAAW;MAChBjE,KAAK,EAAE,6BAA6B;MACpCqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,qBAAqB;MAC1BjE,KAAK,EAAE,wBAAwB;MAC/BqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,4BAA4B;MACjCjE,KAAK,EAAE,gCAAgC;MACvCqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,+BAA+B;MACpCjE,KAAK,EAAE,oCAAoC;MAC3CqE,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEJ,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uDAAuD;IACpEC,OAAO,EAAE,CACP;MACEH,GAAG,EAAE,sBAAsB;MAC3BjE,KAAK,EAAE,8BAA8B;MACrCqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,yBAAyB;MAC9BjE,KAAK,EAAE,gCAAgC;MACvCqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,0BAA0B;MAC/BjE,KAAK,EAAE,4BAA4B;MACnCqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,qBAAqB;MAC1BjE,KAAK,EAAE,uBAAuB;MAC9BqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,oBAAoB;MACzBjE,KAAK,EAAE,sBAAsB;MAC7BqE,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,2CAA2C;IACxDC,OAAO,EAAE,CACP;MACEH,GAAG,EAAE,iBAAiB;MACtBjE,KAAK,EAAE,oBAAoB;MAC3BqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,YAAY;MACjBjE,KAAK,EAAE,aAAa;MACpBqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,kBAAkB;MACvBjE,KAAK,EAAE,qBAAqB;MAC5BqE,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEJ,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,kDAAkD;IAC/DC,OAAO,EAAE,CACP;MACEH,GAAG,EAAE,uBAAuB;MAC5BjE,KAAK,EAAE,mBAAmB;MAC1BqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,SAAS;MACdjE,KAAK,EAAE,SAAS;MAChBqE,OAAO,EAAE;IACX,CAAC,EACD;MACEJ,GAAG,EAAE,KAAK;MACVjE,KAAK,EAAE,KAAK;MACZqE,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEJ,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,sDAAsD;IACnEC,OAAO,EAAE,CACP;MACEH,GAAG,EAAE,cAAc;MACnBjE,KAAK,EAAE,eAAe;MACtBqE,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,CACF;;EAED;EACA,MAAMC,4BAA4B,GAAGzC,QAAQ,CAACtC,UAAU,KAAK,WAAW;;EAExE;EACAtC,SAAS,CAAC,MAAM;IACd,MAAMsH,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAAC5C,SAAS,EAAE;MAEhB,IAAI;QACF4B,YAAY,CAAC,IAAI,CAAC;QAClB,MAAMiB,QAAQ,GAAG,MAAMxF,8BAA8B,CAAC2C,SAAS,EAAE,WAAW,CAAC;QAE7E,IAAI6C,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIF,QAAQ,CAACC,IAAI,CAACA,IAAI,EAAE;UAC/C,MAAMA,IAAI,GAAGE,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI;UAE3F,IAAIA,IAAI,IAAIA,IAAI,CAACI,aAAa,EAAE;YAC9B;YACA/C,WAAW,CAACgD,IAAI,KAAK;cACnB,GAAGA,IAAI;cACP,GAAGL,IAAI,CAACI,aAAa;cACrB1B,MAAM,EAAEsB,IAAI,CAACM,iBAAiB,IAAID,IAAI,CAAC3B;YACzC,CAAC,CAAC,CAAC;UACL;QACF;MACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;QACdnB,eAAe,CAAC,yBAAyB,CAAC;QAC1CF,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,SAAS;QACRJ,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDgB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC5C,SAAS,CAAC,CAAC;EAEf,MAAMsD,kBAAkB,GAAIC,KAAK,IAAMC,KAAK,IAAK;IAC/CrD,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACI,KAAK,GAAGC,KAAK,CAACC,MAAM,CAACC;IACxB,CAAC,CAAC,CAAC;IACHtB,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMuB,kBAAkB,GAAIH,KAAK,IAAK;IACpCrD,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,MAAM,EAAEgC,KAAK,CAACC,MAAM,CAACrF;IACvB,CAAC,CAAC,CAAC;IACHgE,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMwB,sBAAsB,GAAG,MAAOJ,KAAK,IAAK;IAC9C,MAAMK,aAAa,GAAGL,KAAK,CAACC,MAAM,CAACrF,KAAK;IACxC+B,WAAW,CAACgD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPvF,UAAU,EAAEiG;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIA,aAAa,KAAK,WAAW,EAAE;MACjCzB,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;;IAEA;IACA,IAAI,CAACpC,SAAS,EAAE;IAEhB,IAAI;MACF4B,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMiB,QAAQ,GAAG,MAAMxF,8BAA8B,CAAC2C,SAAS,EAAE6D,aAAa,CAAC;MAE/E,IAAIhB,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIF,QAAQ,CAACC,IAAI,CAACA,IAAI,EAAE;QAC/C,MAAMA,IAAI,GAAGE,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI;QAE3F,IAAIA,IAAI,IAAIA,IAAI,CAACI,aAAa,EAAE;UAC9B/C,WAAW,CAACgD,IAAI,KAAK;YACnB,GAAGA,IAAI;YACP,GAAGL,IAAI,CAACI,aAAa;YACrB1B,MAAM,EAAEsB,IAAI,CAACM,iBAAiB,IAAI;UACpC,CAAC,CAAC,CAAC;QACL;MACF;MACAhB,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdnB,eAAe,CAAC,yBAAyB,CAAC;MAC1CF,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,SAAS;MACRJ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC9D,SAAS,IAAI,CAAC2C,4BAA4B,EAAE;IAEjDjB,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF;MACA,MAAM;QAAE9D,UAAU;QAAE4D,MAAM;QAAE,GAAG0B;MAAc,CAAC,GAAGhD,QAAQ;MAEzD,MAAM6D,OAAO,GAAG;QACdb,aAAa;QAAE;QACfE,iBAAiB,EAAE5B;MACrB,CAAC;MAED,MAAMlE,qBAAqB,CAAC0C,SAAS,EAAEE,QAAQ,CAACtC,UAAU,EAAEmG,OAAO,CAAC;MAEpE3B,aAAa,CAAC,KAAK,CAAC;MACpBN,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOuB,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACd/B,eAAe,CAAC,EAAA8B,eAAA,GAAAX,KAAK,CAACR,QAAQ,cAAAmB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBlB,IAAI,cAAAmB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,yBAAyB,CAAC;MAC3ElC,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,SAAS;MACRN,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMyC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrC,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpC,YAAY,CAAC,KAAK,CAAC;IACnBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,IAAIP,SAAS,EAAE;IACb,oBACEnE,OAAA,CAACjC,GAAG;MAAC+C,EAAE,EAAE;QACPC,KAAK,EAAE,MAAM;QACbS,OAAO,EAAE,MAAM;QACfqF,cAAc,EAAE,QAAQ;QACxBnF,UAAU,EAAE,QAAQ;QACpBoF,SAAS,EAAE;MACb,CAAE;MAAA9F,QAAA,eACAhB,OAAA,CAACd,gBAAgB;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEjC,OAAA,CAACjC,GAAG;IAAC+C,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEzBhB,OAAA,CAACjC,GAAG;MAAC+C,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAEjBhB,OAAA,CAACjC,GAAG;QAAC+C,EAAE,EAAE;UAAEiG,QAAQ,EAAE,OAAO;UAAEC,UAAU,EAAE;QAAE,CAAE;QAAAhG,QAAA,eAC5ChB,OAAA,CAAClB,WAAW;UAAEmI,IAAI,EAAC,OAAO;UAC1BnG,EAAE,EAAE;YAACC,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACnBhB,OAAA,CAACf,UAAU;YACTiI,EAAE,EAAC,mBAAmB;YACtBpG,EAAE,EAAE;cACFe,QAAQ,EAAE,MAAM;cAChBM,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE;YACT,CAAE;YAAAZ,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACjB,MAAM;YACLoI,OAAO,EAAC,mBAAmB;YAC3BD,EAAE,EAAC,oBAAoB;YACvBtG,KAAK,EAAE8B,QAAQ,CAACtC,UAAW;YAC3BS,KAAK,EAAC,aAAa;YACnBuG,QAAQ,EAAEhB,sBAAuB;YACjCtF,EAAE,EAAE;cACF,qBAAqB,EAAE;gBACrBe,QAAQ,EAAE,UAAU;gBACpBwF,OAAO,EAAE,WAAW;gBACpBlF,UAAU,EAAE;cACd,CAAC;cACD,oCAAoC,EAAE;gBACpCmF,WAAW,EAAE;cACf,CAAC;cACD,0CAA0C,EAAE;gBAC1CA,WAAW,EAAE;cACf,CAAC;cACD,gDAAgD,EAAE;gBAChDA,WAAW,EAAE;cACf;YACF,CAAE;YAAAtG,QAAA,EAEDX,iBAAiB,CAACkH,GAAG,CAAE5G,MAAM,iBAC5BX,OAAA,CAAChB,QAAQ;cAEP4B,KAAK,EAAED,MAAM,CAACC,KAAM;cACpBE,EAAE,EAAE;gBACFe,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE;cACd,CAAE;cAAAnB,QAAA,EAEDL,MAAM,CAACE;YAAK,GAPRF,MAAM,CAACC,KAAK;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAACkD,4BAA4B,gBAC5BnF,OAAA,CAACG,kBAAkB;MACjBC,UAAU,EAAEsC,QAAQ,CAACtC,UAAW;MAChCC,iBAAiB,EAAEA;IAAkB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,gBAEFjC,OAAA,CAAAE,SAAA;MAAAc,QAAA,gBAEEhB,OAAA,CAAC9B,IAAI;QAAC+C,SAAS,EAAE,CAAE;QAAAD,QAAA,eACjBhB,OAAA,CAACjC,GAAG;UAAC+C,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACjBhB,OAAA,CAAChC,UAAU;YACTkE,OAAO,EAAC,WAAW;YACnBpB,EAAE,EAAE;cACFqB,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE,cAAc;cACrBC,QAAQ,EAAE,MAAM;cAChBX,EAAE,EAAE;YACN,CAAE;YAAAF,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAAChC,UAAU;YACTkE,OAAO,EAAC,OAAO;YACfpB,EAAE,EAAE;cAAEc,KAAK,EAAE,gBAAgB;cAAEV,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,EACxC;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAGbjC,OAAA,CAACjC,GAAG;YAAC+C,EAAE,EAAE;cACPU,OAAO,EAAE,MAAM;cACfgG,mBAAmB,EAAE;gBAAEC,EAAE,EAAE,KAAK;gBAAEC,EAAE,EAAE;cAAc,CAAC;cACrD/F,GAAG,EAAE,CAAC;cACNS,QAAQ,EAAE;YACZ,CAAE;YAAApB,QAAA,EACC6D,gBAAgB,CAAC0C,GAAG,CAAC,CAACI,OAAO,EAAEC,YAAY,kBAC1C5H,OAAA,CAAC9B,IAAI;cAEH+C,SAAS,EAAE,CAAE;cACbH,EAAE,EAAE;gBACFK,CAAC,EAAE,CAAC;gBACJE,MAAM,EAAE,aAAa1C,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;gBAC5C2C,YAAY,EAAE,CAAC;gBACfuG,MAAM,EAAE,OAAO;gBACfC,UAAU,EAAE,sBAAsB;gBAClC,SAAS,EAAE;kBACTC,SAAS,EAAE,aAAapJ,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;kBAC/C2I,WAAW,EAAE3I,KAAK,CAAC,SAAS,EAAE,GAAG;gBACnC;cACF,CAAE;cAAAqC,QAAA,gBAGFhB,OAAA,CAACjC,GAAG;gBAAC+C,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAF,QAAA,gBACjBhB,OAAA,CAAChC,UAAU;kBACTkE,OAAO,EAAC,IAAI;kBACZpB,EAAE,EAAE;oBACFqB,UAAU,EAAE,GAAG;oBACfP,KAAK,EAAE,cAAc;oBACrBC,QAAQ,EAAE,QAAQ;oBAClBX,EAAE,EAAE;kBACN,CAAE;kBAAAF,QAAA,EAED2G,OAAO,CAAC5C;gBAAK;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACbjC,OAAA,CAAChC,UAAU;kBACTkE,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBACFc,KAAK,EAAE,gBAAgB;oBACvBC,QAAQ,EAAE;kBACZ,CAAE;kBAAAb,QAAA,EAED2G,OAAO,CAAC3C;gBAAW;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNjC,OAAA,CAACjC,GAAG;gBAAC+C,EAAE,EAAE;kBACP;kBACAQ,YAAY,EAAE,GAAG;kBACjBH,CAAC,EAAE;kBACH;gBACF,CAAE;gBAAAH,QAAA,EACC2G,OAAO,CAAC1C,OAAO,CAACsC,GAAG,CAAC,CAAC5G,MAAM,EAAEqH,KAAK,kBACjChI,OAAA,CAACjC,GAAG;kBAEF+C,EAAE,EAAE;oBACFU,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBmF,cAAc,EAAE,eAAe;oBAC/BoB,EAAE,EAAE,GAAG;oBACPC,YAAY,EAAEF,KAAK,GAAGL,OAAO,CAAC1C,OAAO,CAACkD,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM;oBACvEC,iBAAiB,EAAEzJ,KAAK,CAAC,SAAS,EAAE,GAAG;kBACzC,CAAE;kBAAAqC,QAAA,gBAEFhB,OAAA,CAACxB,OAAO;oBACNuG,KAAK,EAAEpE,MAAM,CAACuE,OAAQ;oBACtBmD,KAAK;oBACLC,SAAS,EAAC,WAAW;oBACrBxH,EAAE,EAAE;sBACF,uBAAuB,EAAE;wBACvBe,QAAQ,EAAE,UAAU;wBACpBO,QAAQ,EAAE,OAAO;wBACjBmG,UAAU,EAAE;sBACd;oBACF,CAAE;oBAAAvH,QAAA,eAEFhB,OAAA,CAACjC,GAAG;sBAAC+C,EAAE,EAAE;wBACP0H,IAAI,EAAE,CAAC;wBACP;wBACA,SAAS,EAAE;0BACTpH,eAAe,EAAEzC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC;0BACvC2C,YAAY,EAAE,CAAC;0BACf+F,OAAO,EAAE,UAAU;0BACnBhF,MAAM,EAAE;wBACV;sBACF,CAAE;sBAAArB,QAAA,eACAhB,OAAA,CAAChC,UAAU;wBACTkE,OAAO,EAAC,OAAO;wBACfpB,EAAE,EAAE;0BACFqB,UAAU,EAAE,GAAG;0BACfP,KAAK,EAAE,cAAc;0BACrBC,QAAQ,EAAE,QAAQ;0BAClB0G,UAAU,EAAE;wBACd,CAAE;wBAAAvH,QAAA,EAEDL,MAAM,CAACE;sBAAK;wBAAAiB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAEVjC,OAAA,CAAC/B,MAAM;oBACLiI,OAAO,EAAExD,QAAQ,CAAC/B,MAAM,CAACmE,GAAG,CAAE;oBAC9BsC,QAAQ,EAAEtB,kBAAkB,CAACnF,MAAM,CAACmE,GAAG,CAAE;oBACzC2D,UAAU,EAAE;sBAAE,YAAY,EAAE;oBAAa,CAAE;oBAC3C3H,EAAE,EAAE;sBAAE4H,EAAE,EAAE;oBAAE;kBAAE;oBAAA5G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA,GAnDGtB,MAAM,CAACmE,GAAG;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoDZ,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GArGD0F,OAAO,CAAC7C,GAAG;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsGZ,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPjC,OAAA,CAAC9B,IAAI;QACH+C,SAAS,EAAE,CAAE;QACbH,EAAE,EAAE;UACFI,EAAE,EAAE,CAAC;UACLH,KAAK,EAAE,GAAG;UACV+G,UAAU,EAAE,sBAAsB;UAClC,SAAS,EAAE;YACTR,WAAW,EAAE,SAAS;YACtBlG,eAAe,EAAEzC,KAAK,CAAC,SAAS,EAAE,IAAI;UACxC;QACF,CAAE;QAAAqC,QAAA,eAEFhB,OAAA,CAACjC,GAAG;UACF4K,SAAS,EAAC,UAAU;UACpB7H,EAAE,EAAE;YACFO,MAAM,EAAE,WAAW;YACnBiG,WAAW,EAAE,UAAU;YACvBhG,YAAY,EAAE,CAAC;YACfe,MAAM,EAAE,CAAC;YACTuG,QAAQ,EAAE,UAAU;YACpBxH,eAAe,EAAE,OAAO;YACxB0G,UAAU,EAAE,+BAA+B;YAC3C,gBAAgB,EAAE;cAChBR,WAAW,EAAE;YACf,CAAC;YACD,uBAAuB,EAAE;cACvB1F,KAAK,EAAE;YACT;UACF,CAAE;UAAAZ,QAAA,gBAEFhB,OAAA,CAACjC,GAAG;YACF4K,SAAS,EAAC,QAAQ;YAClB7H,EAAE,EAAE;cACFe,QAAQ,EAAE,UAAU;cACpBM,UAAU,EAAE,GAAG;cACf0G,UAAU,EAAE,yCAAyC;cACrDjH,KAAK,EAAE,gBAAgB;cACvByF,OAAO,EAAE,OAAO;cAChByB,UAAU,EAAE,CAAC;cACbhB,UAAU,EAAE;YACd,CAAE;YAAA9G,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENjC,OAAA,CAAC3B,SAAS;YACR0K,SAAS;YACTC,IAAI,EAAE,CAAE;YACRpI,KAAK,EAAE8B,QAAQ,CAACsB,MAAO;YACvBoD,QAAQ,EAAEjB,kBAAmB;YAC7B8C,WAAW,EAAC,iUAAiU;YAC7UC,SAAS;YACThH,OAAO,EAAC,UAAU;YAClBpB,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1B,YAAY,EAAE;kBACZO,MAAM,EAAE;gBACV,CAAC;gBACD,kBAAkB,EAAE;kBAClBA,MAAM,EAAE;gBACV,CAAC;gBACD,wBAAwB,EAAE;kBACxBA,MAAM,EAAE;gBACV;cACF,CAAC;cACD,uBAAuB,EAAE;gBACvBQ,QAAQ,EAAE,UAAU;gBACpB0G,UAAU,EAAE,GAAG;gBACf,gBAAgB,EAAE;kBAChB3G,KAAK,EAAE,gBAAgB;kBACvBuH,OAAO,EAAE;gBACX;cACF;YACF;UAAE;YAAArH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPjC,OAAA,CAACjC,GAAG;QAAC+C,EAAE,EAAE;UAAEsI,EAAE,EAAE,CAAC;UAAE5H,OAAO,EAAE,MAAM;UAAEqF,cAAc,EAAE;QAAa,CAAE;QAAA7F,QAAA,eAChEhB,OAAA,CAACvB,MAAM;UACLyD,OAAO,EAAC,WAAW;UACnB+E,IAAI,EAAC,QAAQ;UACboC,OAAO,EAAE/C,UAAW;UACpBgD,QAAQ,EAAE,CAAC3E,UAAU,IAAIV,QAAS;UAClCnD,EAAE,EAAE;YACFyI,aAAa,EAAE,MAAM;YACrBxC,QAAQ,EAAE,OAAO;YACjByC,EAAE,EAAE;UACN,CAAE;UAAAxI,QAAA,EAEDiD,QAAQ,GAAG,WAAW,GAAG;QAAM;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CACH,eAGDjC,OAAA,CAACpB,QAAQ;MACP6K,IAAI,EAAEpF,WAAY;MAClBqF,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEhD,kBAAmB;MAC5BiD,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA9I,QAAA,eAExDhB,OAAA,CAACnB,KAAK;QACJ8K,OAAO,EAAEhD,kBAAmB;QAC5BoD,QAAQ,EAAC,SAAS;QAClB7H,OAAO,EAAC,QAAQ;QAChBpB,EAAE,EAAE;UACFM,eAAe,EAAE,SAAS;UAC1B,kBAAkB,EAAE;YAClBQ,KAAK,EAAE;UACT;QACF,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXjC,OAAA,CAACpB,QAAQ;MACP6K,IAAI,EAAElF,SAAU;MAChBmF,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE/C,gBAAiB;MAC1BgD,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA9I,QAAA,eAExDhB,OAAA,CAACnB,KAAK;QACJ8K,OAAO,EAAE/C,gBAAiB;QAC1BmD,QAAQ,EAAC,OAAO;QAChB7H,OAAO,EAAC,QAAQ;QAAAlB,QAAA,EAEfyD;MAAY;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACQ,GAAA,CAnrBIF,cAAc;EAAA,QACJ7D,QAAQ;AAAA;AAAAsL,GAAA,GADlBzH,cAAc;AAqrBpB,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAA0H,GAAA;AAAAC,YAAA,CAAA3H,EAAA;AAAA2H,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}