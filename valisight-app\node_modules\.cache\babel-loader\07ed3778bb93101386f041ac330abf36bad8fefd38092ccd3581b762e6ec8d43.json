{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\Companies\\\\Components\\\\ReportSettings.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Switch, Card, Divider, TextField, Button, useTheme, alpha, Snackbar, Alert, CircularProgress, RadioGroup, FormControlLabel, Radio, Collapse, IconButton, Tooltip } from '@mui/material';\nimport { ScheduleOutlined as ComingSoonIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport { getContentSettingsByReportType, updateContentSettings } from '../../../services/contentSettings';\n\n// Fallback component for unsupported report types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComingSoonFallback = ({\n  reportType,\n  reportTypeOptions\n}) => {\n  _s();\n  var _reportTypeOptions$fi;\n  const theme = useTheme();\n  const selectedReportLabel = ((_reportTypeOptions$fi = reportTypeOptions.find(option => option.value === reportType)) === null || _reportTypeOptions$fi === void 0 ? void 0 : _reportTypeOptions$fi.label) || reportType;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 0,\n      sx: {\n        mb: 3,\n        p: 4,\n        backgroundColor: alpha('#1976d2', 0.05),\n        border: `1px solid ${alpha('#1976d2', 0.2)}`,\n        borderRadius: 2,\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(ComingSoonIcon, {\n          sx: {\n            color: '#1976d2',\n            fontSize: '3rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              color: '#1976d2',\n              fontWeight: 600,\n              fontSize: '1.3rem',\n              mb: 1\n            },\n            children: [selectedReportLabel, \" - Coming Soon!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              color: 'text.secondary',\n              fontSize: '1rem',\n              maxWidth: '500px',\n              margin: '0 auto'\n            },\n            children: \"This report type will be implemented soon.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n\n// Collapsible Card Component\n_s(ComingSoonFallback, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = ComingSoonFallback;\nconst CollapsibleCard = ({\n  section,\n  settings,\n  handleSwitchChange,\n  isExpanded,\n  onToggle\n}) => {\n  const handleClick = e => {\n    e.stopPropagation();\n    onToggle(section.key);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    elevation: 0,\n    sx: {\n      border: `1px solid ${alpha('#e0e0e0', 0.8)}`,\n      borderRadius: 2,\n      transition: 'all 0.2s ease-in-out',\n      '&:hover': {\n        boxShadow: `0 2px 8px ${alpha('#1976d2', 0.1)}`,\n        borderColor: alpha('#1976d2', 0.3)\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      onClick: handleClick,\n      sx: {\n        p: 3,\n        cursor: 'pointer',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        '&:hover': {\n          backgroundColor: alpha('#1976d2', 0.02)\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: 'text.primary',\n            fontSize: '1.1rem',\n            mb: 0.5\n          },\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'text.secondary',\n            fontSize: '0.875rem'\n          },\n          children: section.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        sx: {\n          transition: 'transform 0.3s ease',\n          transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'\n        },\n        children: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: isExpanded,\n      timeout: \"auto\",\n      unmountOnExit: true,\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3,\n          pt: 2,\n          height: '350px',\n          overflowY: 'auto'\n        },\n        children: section.options.map((option, index) => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            py: 1.5,\n            borderBottom: index < section.options.length - 1 ? '1px solid' : 'none',\n            borderBottomColor: alpha('#e0e0e0', 0.4)\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            title: option.tooltip,\n            arrow: true,\n            placement: \"top-start\",\n            sx: {\n              '& .MuiTooltip-tooltip': {\n                fontSize: '0.875rem',\n                maxWidth: '400px',\n                lineHeight: 1.4\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1,\n                '&:hover': {\n                  backgroundColor: alpha('#1976d2', 0.04),\n                  borderRadius: 1,\n                  padding: '6px 10px',\n                  margin: '-6px -10px'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  fontWeight: 500,\n                  color: 'text.primary',\n                  fontSize: '0.9rem',\n                  lineHeight: 1.3\n                },\n                children: option.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: settings[option.key],\n            onChange: handleSwitchChange(option.key),\n            inputProps: {\n              'aria-label': 'controlled'\n            },\n            sx: {\n              ml: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, option.key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_c2 = CollapsibleCard;\nconst ReportSettings = ({\n  companyId\n}) => {\n  _s2();\n  const theme = useTheme();\n  const [settings, setSettings] = useState({\n    reportType: 'DEEPSIGHT',\n    incomeSummary: true,\n    netIncome: true,\n    grossProfitMargin: true,\n    netProfitMargin: true,\n    roaAndRoe: true,\n    expensesTopAccounts: true,\n    expensesTopAccountsMonthly: true,\n    expensesWagesVsRevenueMonthly: true,\n    daysSalesOutstanding: true,\n    daysPayablesOutstanding: true,\n    daysInventoryOutstanding: true,\n    cashConversionCycle: true,\n    fixedAssetTurnover: true,\n    netChangeInCash: true,\n    quickRatio: true,\n    monthsCashOnHand: true,\n    thirteenMonthTrailing: true,\n    monthly: true,\n    ytd: true,\n    balanceSheet: true,\n    prompt: \"\"\n  });\n  const [isSaving, setIsSaving] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showSuccess, setShowSuccess] = useState(false);\n  const [showError, setShowError] = useState(false);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [hasChanges, setHasChanges] = useState(false);\n  const [expandedCards, setExpandedCards] = useState({});\n\n  // Toggle individual card\n  const toggleCard = cardKey => {\n    setExpandedCards(prev => ({\n      ...prev,\n      [cardKey]: !prev[cardKey]\n    }));\n  };\n\n  // Report type options\n  const reportTypeOptions = [{\n    value: 'DEEPSIGHT',\n    label: 'Deepsight'\n  }, {\n    value: \"profitpulse\",\n    label: 'ProfitPulse (Monthly)'\n  }, {\n    value: \"kpitrack\",\n    label: 'KPITrack (Benchmark)'\n  }, {\n    value: \"gaap\",\n    label: 'GAAP Align'\n  }, {\n    value: \"fincheck\",\n    label: 'FinCheck (Current State)'\n  }, {\n    value: \"flowcast\",\n    label: 'FlowCast (13 Week)'\n  }];\n\n  // Settings sections configuration\n  const settingsSections = [{\n    key: 'currentFiscalYear',\n    title: 'Current Fiscal Year',\n    description: 'Key performance metrics for the current fiscal period',\n    options: [{\n      key: 'incomeSummary',\n      label: 'Monthly Performance Breakdown (Income Summary)',\n      tooltip: 'Provides a monthly breakdown of income performance showing revenue trends and patterns throughout the fiscal year.'\n    }, {\n      key: 'netIncome',\n      label: 'Net Income/(Loss)',\n      tooltip: 'Shows the companys bottom-line profit or loss after deducting all expenses, taxes, and interest for the current fiscal year.'\n    }, {\n      key: 'grossProfitMargin',\n      label: 'Gross Profit Margin',\n      tooltip: 'Displays the percentage of revenue remaining after accounting for the cost of goods sold (COGS). Useful for measuring core business profitability.'\n    }, {\n      key: 'netProfitMargin',\n      label: 'Net Profit Margin',\n      tooltip: 'Shows the percentage of revenue converted into net profit after all expenses. Reflects overall efficiency in turning sales into profit.'\n    }]\n  }, {\n    key: 'expenseSummary',\n    title: 'Expense Summary',\n    description: 'Detailed analysis of company expenses and efficiency ratios',\n    options: [{\n      key: 'roaAndRoe',\n      label: 'Return on Assets and Equity',\n      tooltip: 'Return on Assets (ROA) measures how efficiently assets generate profit. Return on Equity (ROE) shows how effectively shareholder equity is used to deliver returns.'\n    }, {\n      key: 'expensesTopAccounts',\n      label: 'Expenses: Top Accounts',\n      tooltip: 'Identifies and analyzes the highest expense categories, helping prioritize cost management efforts.'\n    }, {\n      key: 'expensesTopAccountsMonthly',\n      label: 'Expenses: Top Accounts Monthly',\n      tooltip: 'Monthly breakdown of top expense accounts showing spending patterns and trends over time.'\n    }, {\n      key: 'expensesWagesVsRevenueMonthly',\n      label: 'Expenses: Wages Vs Revenue Monthly',\n      tooltip: 'Compares monthly wage expenses against revenue to track labor cost efficiency and productivity trends.'\n    }]\n  }, {\n    key: 'operationalEfficiency',\n    title: 'Operational Efficiency',\n    description: 'Key operational metrics measuring business efficiency',\n    options: [{\n      key: 'daysSalesOutstanding',\n      label: 'Days Sales (A/R) Outstanding',\n      tooltip: 'Measures the average number of days it takes to collect receivables. Lower values indicate faster cash collection.'\n    }, {\n      key: 'daysPayablesOutstanding',\n      label: 'Days Payables (AP) Outstanding',\n      tooltip: 'Shows the average number of days the company takes to pay suppliers. Helps assess payment timing and cash flow management.'\n    }, {\n      key: 'daysInventoryOutstanding',\n      label: 'Days Inventory Outstanding',\n      tooltip: 'Measures how many days of inventory the company holds on average. Lower values may indicate efficient inventory management.'\n    }, {\n      key: 'cashConversionCycle',\n      label: 'Cash Conversion Cycle',\n      tooltip: 'The time it takes to convert inventory investments into cash flows from sales. Shorter cycles indicate better working capital management.'\n    }, {\n      key: 'fixedAssetTurnover',\n      label: 'Fixed Asset Turnover',\n      tooltip: 'Measures how efficiently the company uses its fixed assets to generate sales. Higher ratios indicate better asset utilization.'\n    }]\n  }, {\n    key: 'liquiditySummary',\n    title: 'Liquidity Summary',\n    description: 'Cash flow and liquidity position analysis',\n    options: [{\n      key: 'netChangeInCash',\n      label: 'Net Change in Cash',\n      tooltip: 'Shows the overall increase or decrease in cash position over the period, indicating cash flow health.'\n    }, {\n      key: 'quickRatio',\n      label: 'Quick Ratio',\n      tooltip: 'Measures the company\\'s ability to pay short-term debts using the most liquid assets. Higher ratios indicate better liquidity.'\n    }, {\n      key: 'monthsCashOnHand',\n      label: 'Months Cash on Hand',\n      tooltip: 'Indicates how many months the company can operate with current cash reserves, providing insight into financial runway.'\n    }]\n  }, {\n    key: 'profitAndLoss',\n    title: 'Profit and Loss',\n    description: 'Comprehensive profit and loss statement analysis',\n    options: [{\n      key: 'thirteenMonthTrailing',\n      label: '13 Month Trailing',\n      tooltip: 'Provides a 13-month trailing view of profit and loss, smoothing seasonal variations and showing longer-term trends.'\n    }, {\n      key: 'monthly',\n      label: 'Monthly',\n      tooltip: 'Monthly profit and loss breakdown showing month-to-month performance and identifying seasonal patterns.'\n    }, {\n      key: 'ytd',\n      label: 'YTD',\n      tooltip: 'Year-to-date profit and loss summary comparing current performance against the full fiscal year.'\n    }]\n  }, {\n    key: 'balanceSheet',\n    title: 'Balance Sheet',\n    description: 'Complete balance sheet analysis and position summary',\n    options: [{\n      key: 'balanceSheet',\n      label: 'Balance Sheet',\n      tooltip: 'Comprehensive balance sheet showing assets, liabilities, and equity positions with period-over-period comparisons.'\n    }]\n  }];\n  const isCurrentReportTypeSupported = settings.reportType === 'DEEPSIGHT';\n\n  // Load existing settings on component mount\n  useEffect(() => {\n    const loadSettings = async () => {\n      if (!companyId) return;\n      try {\n        setIsLoading(true);\n        const response = await getContentSettingsByReportType(companyId, 'DEEPSIGHT');\n        if (response.data.success && response.data.data) {\n          const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\n          if (data && data.chartSettings) {\n            setSettings(prev => ({\n              ...prev,\n              ...data.chartSettings,\n              prompt: data.promptDescription || prev.prompt\n            }));\n          }\n        }\n      } catch (error) {\n        setErrorMessage('Failed to load settings');\n        setShowError(true);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    loadSettings();\n  }, [companyId]);\n  const handleSwitchChange = field => event => {\n    setSettings(prev => ({\n      ...prev,\n      [field]: event.target.checked\n    }));\n    setHasChanges(true);\n  };\n  const handlePromptChange = event => {\n    setSettings(prev => ({\n      ...prev,\n      prompt: event.target.value\n    }));\n    setHasChanges(true);\n  };\n  const handleReportTypeChange = async event => {\n    const newReportType = event.target.value;\n    setSettings(prev => ({\n      ...prev,\n      reportType: newReportType\n    }));\n    if (newReportType !== 'DEEPSIGHT') {\n      setHasChanges(false);\n      return;\n    }\n    if (!companyId) return;\n    try {\n      setIsLoading(true);\n      const response = await getContentSettingsByReportType(companyId, newReportType);\n      if (response.data.success && response.data.data) {\n        const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\n        if (data && data.chartSettings) {\n          setSettings(prev => ({\n            ...prev,\n            ...data.chartSettings,\n            prompt: data.promptDescription || ''\n          }));\n        }\n      }\n      setHasChanges(false);\n    } catch (error) {\n      setErrorMessage('Failed to load settings');\n      setShowError(true);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSave = async () => {\n    if (!companyId || !isCurrentReportTypeSupported) return;\n    setIsSaving(true);\n    try {\n      const {\n        reportType,\n        prompt,\n        ...chartSettings\n      } = settings;\n      const payload = {\n        chartSettings,\n        promptDescription: prompt\n      };\n      await updateContentSettings(companyId, settings.reportType, payload);\n      setHasChanges(false);\n      setShowSuccess(true);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setErrorMessage(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to save settings');\n      setShowError(true);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleCloseSuccess = () => {\n    setShowSuccess(false);\n  };\n  const handleCloseError = () => {\n    setShowError(false);\n    setErrorMessage('');\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          fontWeight: 600,\n          color: 'text.primary',\n          fontSize: '1rem',\n          mb: 2\n        },\n        children: \"Report Type\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n        value: settings.reportType,\n        onChange: handleReportTypeChange,\n        sx: {\n          display: 'flex',\n          flexDirection: 'row',\n          flexWrap: 'wrap',\n          gap: 2\n        },\n        children: reportTypeOptions.map(option => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative',\n            transition: 'all 0.2s ease-in-out',\n            cursor: 'pointer',\n            borderRadius: 2,\n            backgroundColor: settings.reportType === option.value ? alpha('#1976d2', 0.08) : 'white',\n            '&:hover': {\n              backgroundColor: settings.reportType === option.value ? alpha('#1976d2', 0.12) : alpha('#1976d2', 0.04),\n              transform: 'translateY(-2px)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: option.value,\n            control: /*#__PURE__*/_jsxDEV(Radio, {\n              sx: {\n                color: 'grey.400',\n                '&.Mui-checked': {\n                  color: '#1976d2'\n                },\n                '&:hover': {\n                  backgroundColor: 'transparent'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 19\n            }, this),\n            label: /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                fontWeight: settings.reportType === option.value ? 600 : 500,\n                fontSize: '0.875rem',\n                color: settings.reportType === option.value ? '#1976d2' : 'text.primary',\n                transition: 'all 0.2s ease-in-out'\n              },\n              children: option.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 19\n            }, this),\n            sx: {\n              m: 0,\n              px: 2,\n              py: 1.5,\n              width: '100%',\n              '& .MuiFormControlLabel-label': {\n                userSelect: 'none'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this)\n        }, option.value, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this), !isCurrentReportTypeSupported ? /*#__PURE__*/_jsxDEV(ComingSoonFallback, {\n      reportType: settings.reportType,\n      reportTypeOptions: reportTypeOptions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 600,\n            color: 'text.primary',\n            fontSize: '1rem',\n            mb: 1\n          },\n          children: \"Report Components\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'text.secondary',\n            mb: 3\n          },\n          children: \"Select which financial metrics to include in your analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'grid',\n            gridTemplateColumns: {\n              xs: '1fr',\n              md: '1fr 1fr 1fr'\n            },\n            gap: 3,\n            maxWidth: '100%',\n            alignItems: 'start'\n          },\n          children: settingsSections.map(section => /*#__PURE__*/_jsxDEV(CollapsibleCard, {\n            section: section,\n            settings: settings,\n            handleSwitchChange: handleSwitchChange,\n            isExpanded: expandedCards[section.key] || false,\n            onToggle: toggleCard\n          }, section.key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        elevation: 0,\n        sx: {\n          mb: 3,\n          width: 600,\n          transition: 'all 0.2s ease-in-out',\n          '&:hover': {\n            borderColor: '#1976d2',\n            backgroundColor: alpha('#1976d2', 0.02)\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          component: \"fieldset\",\n          sx: {\n            border: '2px solid',\n            borderColor: 'grey.400',\n            borderRadius: 1,\n            margin: 0,\n            position: 'relative',\n            backgroundColor: 'white',\n            transition: 'border-color 0.2s ease-in-out',\n            '&:focus-within': {\n              borderColor: '#1976d2'\n            },\n            '&:focus-within legend': {\n              color: '#1976d2'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            component: \"legend\",\n            sx: {\n              fontSize: '0.875rem',\n              fontWeight: 500,\n              fontFamily: '\"Roboto\",\"Helvetica\",\"Arial\",sans-serif',\n              color: 'text.secondary',\n              padding: '0 2px',\n              marginLeft: 1,\n              transition: 'color 0.2s ease-in-out'\n            },\n            children: \"Prompt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            multiline: true,\n            rows: 5,\n            value: settings.prompt,\n            onChange: handlePromptChange,\n            placeholder: \"Sample: Summarize financial performance for the current fiscal year, covering expenses, operational efficiency, liquidity, and profit & loss (monthly, YTD, and 13-month trailing). Include a balance sheet review, cost and COGS analysis, and conclude with executive insights, key focus areas, and recommendations for 2025\",\n            fullWidth: true,\n            variant: \"outlined\",\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                '& fieldset': {\n                  border: 'none'\n                },\n                '&:hover fieldset': {\n                  border: 'none'\n                },\n                '&.Mui-focused fieldset': {\n                  border: 'none'\n                }\n              },\n              '& .MuiInputBase-input': {\n                fontSize: '0.875rem',\n                lineHeight: 1.6,\n                '&::placeholder': {\n                  color: 'text.secondary',\n                  opacity: 0.7\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4,\n          display: 'flex',\n          justifyContent: 'flex-start'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"medium\",\n          onClick: handleSave,\n          disabled: !hasChanges || isSaving,\n          sx: {\n            textTransform: 'none',\n            minWidth: '120px',\n            px: 3\n          },\n          children: isSaving ? 'SAVING...' : 'SAVE'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 791,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showSuccess,\n      autoHideDuration: 4000,\n      onClose: handleCloseSuccess,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSuccess,\n        severity: \"success\",\n        variant: \"filled\",\n        sx: {\n          backgroundColor: '#1976d2',\n          '& .MuiAlert-icon': {\n            color: 'white'\n          }\n        },\n        children: \"Settings saved successfully!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 810,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showError,\n      autoHideDuration: 6000,\n      onClose: handleCloseError,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseError,\n        severity: \"error\",\n        variant: \"filled\",\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 838,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 832,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 571,\n    columnNumber: 5\n  }, this);\n};\n_s2(ReportSettings, \"lUxQy9DixT4IJXa8nlvBZF8GgrQ=\", false, function () {\n  return [useTheme];\n});\n_c3 = ReportSettings;\nexport default ReportSettings;\nZ;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ComingSoonFallback\");\n$RefreshReg$(_c2, \"CollapsibleCard\");\n$RefreshReg$(_c3, \"ReportSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Switch", "Card", "Divider", "TextField", "<PERSON><PERSON>", "useTheme", "alpha", "Snackbar", "<PERSON><PERSON>", "CircularProgress", "RadioGroup", "FormControlLabel", "Radio", "Collapse", "IconButton", "<PERSON><PERSON><PERSON>", "ScheduleOutlined", "ComingSoonIcon", "ExpandMore", "ExpandMoreIcon", "getContentSettingsByReportType", "updateContentSettings", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComingSoon<PERSON>allback", "reportType", "reportTypeOptions", "_s", "_reportTypeOptions$fi", "theme", "selectedReport<PERSON><PERSON>l", "find", "option", "value", "label", "sx", "width", "children", "elevation", "mb", "p", "backgroundColor", "border", "borderRadius", "textAlign", "display", "flexDirection", "alignItems", "gap", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "max<PERSON><PERSON><PERSON>", "margin", "_c", "CollapsibleCard", "section", "settings", "handleSwitchChange", "isExpanded", "onToggle", "handleClick", "e", "stopPropagation", "key", "transition", "boxShadow", "borderColor", "onClick", "cursor", "justifyContent", "flex", "title", "description", "size", "transform", "in", "timeout", "unmountOnExit", "pt", "height", "overflowY", "options", "map", "index", "py", "borderBottom", "length", "borderBottomColor", "tooltip", "arrow", "placement", "lineHeight", "padding", "checked", "onChange", "inputProps", "ml", "_c2", "ReportSettings", "companyId", "_s2", "setSettings", "incomeSummary", "netIncome", "grossProfitMargin", "netProfitMargin", "roaAndRoe", "expensesTopAccounts", "expensesTopAccountsMonthly", "expensesWagesVsRevenueMonthly", "daysSalesOutstanding", "daysPayablesOutstanding", "daysInventoryOutstanding", "cashConversionCycle", "fixedAssetTurnover", "netChangeInCash", "quickRatio", "monthsCashOnHand", "thirteenMonthTrailing", "monthly", "ytd", "balanceSheet", "prompt", "isSaving", "setIsSaving", "isLoading", "setIsLoading", "showSuccess", "setShowSuccess", "showError", "setShowError", "errorMessage", "setErrorMessage", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "expandedCards", "setExpandedCards", "toggleCard", "card<PERSON><PERSON>", "prev", "settingsSections", "isCurrentReportTypeSupported", "loadSettings", "response", "data", "success", "Array", "isArray", "chartSettings", "promptDescription", "error", "field", "event", "target", "handlePromptChange", "handleReportTypeChange", "newReportType", "handleSave", "payload", "_error$response", "_error$response$data", "message", "handleCloseSuccess", "handleCloseError", "minHeight", "flexWrap", "position", "control", "m", "px", "userSelect", "gridTemplateColumns", "xs", "md", "component", "fontFamily", "marginLeft", "multiline", "rows", "placeholder", "fullWidth", "opacity", "mt", "disabled", "textTransform", "min<PERSON><PERSON><PERSON>", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "_c3", "Z", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/Companies/Components/ReportSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Switch,\r\n  Card,\r\n  Divider,\r\n  TextField,\r\n  Button,\r\n  useTheme,\r\n  alpha,\r\n  Snackbar,\r\n  Alert,\r\n  CircularProgress,\r\n  RadioGroup,\r\n  FormControlLabel,\r\n  Radio,\r\n  Collapse,\r\n  IconButton,\r\n  Tooltip,\r\n} from '@mui/material';\r\n\r\nimport {\r\n  ScheduleOutlined as ComingSoonIcon,\r\n  ExpandMore as ExpandMoreIcon,\r\n} from '@mui/icons-material';\r\n\r\nimport {\r\n  getContentSettingsByReportType,\r\n  updateContentSettings\r\n} from '../../../services/contentSettings';\r\n\r\n// Fallback component for unsupported report types\r\nconst ComingSoonFallback = ({ reportType, reportTypeOptions }) => {\r\n  const theme = useTheme();\r\n  \r\n  const selectedReportLabel = reportTypeOptions.find(\r\n    option => option.value === reportType\r\n  )?.label || reportType;\r\n\r\n  return (\r\n    <Box sx={{ width: '100%' }}>\r\n      <Card\r\n        elevation={0}\r\n        sx={{\r\n          mb: 3,\r\n          p: 4,\r\n          backgroundColor: alpha('#1976d2', 0.05),\r\n          border: `1px solid ${alpha('#1976d2', 0.2)}`,\r\n          borderRadius: 2,\r\n          textAlign: 'center',\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>\r\n          <ComingSoonIcon sx={{ color: '#1976d2', fontSize: '3rem' }} />\r\n          <Box>\r\n            <Typography\r\n              variant=\"h5\"\r\n              sx={{\r\n                color: '#1976d2',\r\n                fontWeight: 600,\r\n                fontSize: '1.3rem',\r\n                mb: 1,\r\n              }}\r\n            >\r\n              {selectedReportLabel} - Coming Soon!\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body1\"\r\n              sx={{\r\n                color: 'text.secondary',\r\n                fontSize: '1rem',\r\n                maxWidth: '500px',\r\n                margin: '0 auto',\r\n              }}\r\n            >\r\n              This report type will be implemented soon.\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n      </Card>\r\n    </Box>\r\n  );\r\n};\r\n\r\n// Collapsible Card Component\r\nconst CollapsibleCard = ({ section, settings, handleSwitchChange, isExpanded, onToggle }) => {\r\n  const handleClick = (e) => {\r\n    e.stopPropagation();\r\n    onToggle(section.key);\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      elevation={0}\r\n      sx={{\r\n        border: `1px solid ${alpha('#e0e0e0', 0.8)}`,\r\n        borderRadius: 2,\r\n        transition: 'all 0.2s ease-in-out',\r\n        '&:hover': {\r\n          boxShadow: `0 2px 8px ${alpha('#1976d2', 0.1)}`,\r\n          borderColor: alpha('#1976d2', 0.3),\r\n        },\r\n      }}\r\n    >\r\n      {/* Card Header - Clickable */}\r\n      <Box\r\n        onClick={handleClick}\r\n        sx={{\r\n          p: 3,\r\n          cursor: 'pointer',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'space-between',\r\n          '&:hover': {\r\n            backgroundColor: alpha('#1976d2', 0.02),\r\n          },\r\n        }}\r\n      >\r\n        <Box sx={{ flex: 1 }}>\r\n          <Typography\r\n            variant=\"h6\"\r\n            sx={{\r\n              fontWeight: 600,\r\n              color: 'text.primary',\r\n              fontSize: '1.1rem',\r\n              mb: 0.5,\r\n            }}\r\n          >\r\n            {section.title}\r\n          </Typography>\r\n          <Typography\r\n            variant=\"body2\"\r\n            sx={{ \r\n              color: 'text.secondary',\r\n              fontSize: '0.875rem',\r\n            }}\r\n          >\r\n            {section.description}\r\n          </Typography>\r\n        </Box>\r\n        <IconButton\r\n          size=\"small\"\r\n          sx={{\r\n            transition: 'transform 0.3s ease',\r\n            transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',\r\n          }}\r\n        >\r\n          <ExpandMoreIcon />\r\n        </IconButton>\r\n      </Box>\r\n\r\n      {/* Collapsible Content */}\r\n      <Collapse in={isExpanded} timeout=\"auto\" unmountOnExit>\r\n        <Divider />\r\n        <Box sx={{ p: 3, pt: 2, height: '350px', overflowY: 'auto' }}>\r\n          {section.options.map((option, index) => (\r\n            <Box \r\n              key={option.key}\r\n              sx={{\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'space-between',\r\n                py: 1.5,\r\n                borderBottom: index < section.options.length - 1 ? '1px solid' : 'none',\r\n                borderBottomColor: alpha('#e0e0e0', 0.4),\r\n              }}\r\n            >\r\n              <Tooltip \r\n                title={option.tooltip}\r\n                arrow\r\n                placement=\"top-start\"\r\n                sx={{\r\n                  '& .MuiTooltip-tooltip': {\r\n                    fontSize: '0.875rem',\r\n                    maxWidth: '400px',\r\n                    lineHeight: 1.4,\r\n                  }\r\n                }}\r\n              >\r\n                <Box sx={{ \r\n                  flex: 1,\r\n                  '&:hover': {\r\n                    backgroundColor: alpha('#1976d2', 0.04),\r\n                    borderRadius: 1,\r\n                    padding: '6px 10px',\r\n                    margin: '-6px -10px',\r\n                  }\r\n                }}>\r\n                  <Typography\r\n                    variant=\"body1\"\r\n                    sx={{\r\n                      fontWeight: 500,\r\n                      color: 'text.primary',\r\n                      fontSize: '0.9rem',\r\n                      lineHeight: 1.3,\r\n                    }}\r\n                  >\r\n                    {option.label}\r\n                  </Typography>\r\n                </Box>\r\n              </Tooltip>\r\n\r\n              <Switch\r\n                checked={settings[option.key]}\r\n                onChange={handleSwitchChange(option.key)}\r\n                inputProps={{ 'aria-label': 'controlled' }}\r\n                sx={{ ml: 2 }}\r\n              />\r\n            </Box>\r\n          ))}\r\n        </Box>\r\n      </Collapse>\r\n    </Card>\r\n  );\r\n};\r\n\r\nconst ReportSettings = ({ companyId }) => {\r\n  const theme = useTheme();\r\n  const [settings, setSettings] = useState({\r\n    reportType: 'DEEPSIGHT',\r\n    incomeSummary: true,\r\n    netIncome: true,\r\n    grossProfitMargin: true,\r\n    netProfitMargin: true,\r\n    roaAndRoe: true,\r\n    expensesTopAccounts: true,\r\n    expensesTopAccountsMonthly: true,\r\n    expensesWagesVsRevenueMonthly: true,\r\n    daysSalesOutstanding: true,\r\n    daysPayablesOutstanding: true,\r\n    daysInventoryOutstanding: true,\r\n    cashConversionCycle: true,\r\n    fixedAssetTurnover: true,\r\n    netChangeInCash: true,\r\n    quickRatio: true,\r\n    monthsCashOnHand: true,\r\n    thirteenMonthTrailing: true,\r\n    monthly: true,\r\n    ytd: true,\r\n    balanceSheet: true,\r\n    prompt: \"\",\r\n  });\r\n\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [showSuccess, setShowSuccess] = useState(false);\r\n  const [showError, setShowError] = useState(false);\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [hasChanges, setHasChanges] = useState(false);\r\n  const [expandedCards, setExpandedCards] = useState({});\r\n\r\n  // Toggle individual card\r\n  const toggleCard = (cardKey) => {\r\n    setExpandedCards(prev => ({\r\n      ...prev,\r\n      [cardKey]: !prev[cardKey]\r\n    }));\r\n  };\r\n\r\n  // Report type options\r\n  const reportTypeOptions = [\r\n    {\r\n      value: 'DEEPSIGHT',\r\n      label: 'Deepsight'\r\n    },\r\n    {\r\n      value: \"profitpulse\",\r\n      label: 'ProfitPulse (Monthly)'\r\n    },\r\n    {\r\n      value: \"kpitrack\",\r\n      label: 'KPITrack (Benchmark)'\r\n    },\r\n    {\r\n      value: \"gaap\",\r\n      label: 'GAAP Align'\r\n    },\r\n    {\r\n      value: \"fincheck\",\r\n      label: 'FinCheck (Current State)'\r\n    },\r\n    {\r\n      value: \"flowcast\",\r\n      label: 'FlowCast (13 Week)'\r\n    }\r\n  ];\r\n\r\n  // Settings sections configuration\r\n  const settingsSections = [\r\n    {\r\n      key: 'currentFiscalYear',\r\n      title: 'Current Fiscal Year',\r\n      description: 'Key performance metrics for the current fiscal period',\r\n      options: [\r\n        {\r\n          key: 'incomeSummary',\r\n          label: 'Monthly Performance Breakdown (Income Summary)',\r\n          tooltip: 'Provides a monthly breakdown of income performance showing revenue trends and patterns throughout the fiscal year.'\r\n        },\r\n        {\r\n          key: 'netIncome',\r\n          label: 'Net Income/(Loss)',\r\n          tooltip: 'Shows the companys bottom-line profit or loss after deducting all expenses, taxes, and interest for the current fiscal year.'\r\n        },\r\n        {\r\n          key: 'grossProfitMargin',\r\n          label: 'Gross Profit Margin',\r\n          tooltip: 'Displays the percentage of revenue remaining after accounting for the cost of goods sold (COGS). Useful for measuring core business profitability.'\r\n        },\r\n        {\r\n          key: 'netProfitMargin',\r\n          label: 'Net Profit Margin',\r\n          tooltip: 'Shows the percentage of revenue converted into net profit after all expenses. Reflects overall efficiency in turning sales into profit.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'expenseSummary',\r\n      title: 'Expense Summary',\r\n      description: 'Detailed analysis of company expenses and efficiency ratios',\r\n      options: [\r\n        {\r\n          key: 'roaAndRoe',\r\n          label: 'Return on Assets and Equity',\r\n          tooltip: 'Return on Assets (ROA) measures how efficiently assets generate profit. Return on Equity (ROE) shows how effectively shareholder equity is used to deliver returns.'\r\n        },\r\n        {\r\n          key: 'expensesTopAccounts',\r\n          label: 'Expenses: Top Accounts',\r\n          tooltip: 'Identifies and analyzes the highest expense categories, helping prioritize cost management efforts.'\r\n        },\r\n        {\r\n          key: 'expensesTopAccountsMonthly',\r\n          label: 'Expenses: Top Accounts Monthly',\r\n          tooltip: 'Monthly breakdown of top expense accounts showing spending patterns and trends over time.'\r\n        },\r\n        {\r\n          key: 'expensesWagesVsRevenueMonthly',\r\n          label: 'Expenses: Wages Vs Revenue Monthly',\r\n          tooltip: 'Compares monthly wage expenses against revenue to track labor cost efficiency and productivity trends.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'operationalEfficiency',\r\n      title: 'Operational Efficiency',\r\n      description: 'Key operational metrics measuring business efficiency',\r\n      options: [\r\n        {\r\n          key: 'daysSalesOutstanding',\r\n          label: 'Days Sales (A/R) Outstanding',\r\n          tooltip: 'Measures the average number of days it takes to collect receivables. Lower values indicate faster cash collection.'\r\n        },\r\n        {\r\n          key: 'daysPayablesOutstanding',\r\n          label: 'Days Payables (AP) Outstanding',\r\n          tooltip: 'Shows the average number of days the company takes to pay suppliers. Helps assess payment timing and cash flow management.'\r\n        },\r\n        {\r\n          key: 'daysInventoryOutstanding',\r\n          label: 'Days Inventory Outstanding',\r\n          tooltip: 'Measures how many days of inventory the company holds on average. Lower values may indicate efficient inventory management.'\r\n        },\r\n        {\r\n          key: 'cashConversionCycle',\r\n          label: 'Cash Conversion Cycle',\r\n          tooltip: 'The time it takes to convert inventory investments into cash flows from sales. Shorter cycles indicate better working capital management.'\r\n        },\r\n        {\r\n          key: 'fixedAssetTurnover',\r\n          label: 'Fixed Asset Turnover',\r\n          tooltip: 'Measures how efficiently the company uses its fixed assets to generate sales. Higher ratios indicate better asset utilization.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'liquiditySummary',\r\n      title: 'Liquidity Summary',\r\n      description: 'Cash flow and liquidity position analysis',\r\n      options: [\r\n        {\r\n          key: 'netChangeInCash',\r\n          label: 'Net Change in Cash',\r\n          tooltip: 'Shows the overall increase or decrease in cash position over the period, indicating cash flow health.'\r\n        },\r\n        {\r\n          key: 'quickRatio',\r\n          label: 'Quick Ratio',\r\n          tooltip: 'Measures the company\\'s ability to pay short-term debts using the most liquid assets. Higher ratios indicate better liquidity.'\r\n        },\r\n        {\r\n          key: 'monthsCashOnHand',\r\n          label: 'Months Cash on Hand',\r\n          tooltip: 'Indicates how many months the company can operate with current cash reserves, providing insight into financial runway.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'profitAndLoss',\r\n      title: 'Profit and Loss',\r\n      description: 'Comprehensive profit and loss statement analysis',\r\n      options: [\r\n        {\r\n          key: 'thirteenMonthTrailing',\r\n          label: '13 Month Trailing',\r\n          tooltip: 'Provides a 13-month trailing view of profit and loss, smoothing seasonal variations and showing longer-term trends.'\r\n        },\r\n        {\r\n          key: 'monthly',\r\n          label: 'Monthly',\r\n          tooltip: 'Monthly profit and loss breakdown showing month-to-month performance and identifying seasonal patterns.'\r\n        },\r\n        {\r\n          key: 'ytd',\r\n          label: 'YTD',\r\n          tooltip: 'Year-to-date profit and loss summary comparing current performance against the full fiscal year.'\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      key: 'balanceSheet',\r\n      title: 'Balance Sheet',\r\n      description: 'Complete balance sheet analysis and position summary',\r\n      options: [\r\n        {\r\n          key: 'balanceSheet',\r\n          label: 'Balance Sheet',\r\n          tooltip: 'Comprehensive balance sheet showing assets, liabilities, and equity positions with period-over-period comparisons.'\r\n        },\r\n      ]\r\n    },\r\n  ];\r\n\r\n  const isCurrentReportTypeSupported = settings.reportType === 'DEEPSIGHT';\r\n\r\n  // Load existing settings on component mount\r\n  useEffect(() => {\r\n    const loadSettings = async () => {\r\n      if (!companyId) return;\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        const response = await getContentSettingsByReportType(companyId, 'DEEPSIGHT');\r\n\r\n        if (response.data.success && response.data.data) {\r\n          const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\r\n          \r\n          if (data && data.chartSettings) {\r\n            setSettings(prev => ({\r\n              ...prev,\r\n              ...data.chartSettings,\r\n              prompt: data.promptDescription || prev.prompt,\r\n            }));\r\n          }\r\n        }\r\n      } catch (error) {\r\n        setErrorMessage('Failed to load settings');\r\n        setShowError(true);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    loadSettings();\r\n  }, [companyId]);\r\n\r\n  const handleSwitchChange = (field) => (event) => {\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      [field]: event.target.checked\r\n    }));\r\n    setHasChanges(true);\r\n  };\r\n\r\n  const handlePromptChange = (event) => {\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      prompt: event.target.value\r\n    }));\r\n    setHasChanges(true);\r\n  };\r\n\r\n  const handleReportTypeChange = async (event) => {\r\n    const newReportType = event.target.value;\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      reportType: newReportType\r\n    }));\r\n\r\n    if (newReportType !== 'DEEPSIGHT') {\r\n      setHasChanges(false);\r\n      return;\r\n    }\r\n\r\n    if (!companyId) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await getContentSettingsByReportType(companyId, newReportType);\r\n\r\n      if (response.data.success && response.data.data) {\r\n        const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\r\n        \r\n        if (data && data.chartSettings) {\r\n          setSettings(prev => ({\r\n            ...prev,\r\n            ...data.chartSettings,\r\n            prompt: data.promptDescription || '',\r\n          }));\r\n        }\r\n      }\r\n      setHasChanges(false);\r\n    } catch (error) {\r\n      setErrorMessage('Failed to load settings');\r\n      setShowError(true);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!companyId || !isCurrentReportTypeSupported) return;\r\n\r\n    setIsSaving(true);\r\n\r\n    try {\r\n      const { reportType, prompt, ...chartSettings } = settings;\r\n      \r\n      const payload = {\r\n        chartSettings,\r\n        promptDescription: prompt,\r\n      };\r\n\r\n      await updateContentSettings(companyId, settings.reportType, payload);\r\n\r\n      setHasChanges(false);\r\n      setShowSuccess(true);\r\n    } catch (error) {\r\n      setErrorMessage(error.response?.data?.message || 'Failed to save settings');\r\n      setShowError(true);\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCloseSuccess = () => {\r\n    setShowSuccess(false);\r\n  };\r\n\r\n  const handleCloseError = () => {\r\n    setShowError(false);\r\n    setErrorMessage('');\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Box sx={{\r\n        width: '100%',\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        minHeight: '400px'\r\n      }}>\r\n        <CircularProgress />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box sx={{ width: '100%' }}>\r\n      {/* Header Section with Radio Buttons */}\r\n      <Box sx={{ mb: 4 }}>\r\n        <Typography\r\n          variant=\"subtitle1\"\r\n          sx={{\r\n            fontWeight: 600,\r\n            color: 'text.primary',\r\n            fontSize: '1rem',\r\n            mb: 2,\r\n          }}\r\n        >\r\n          Report Type\r\n        </Typography>\r\n        \r\n        <RadioGroup\r\n          value={settings.reportType}\r\n          onChange={handleReportTypeChange}\r\n          sx={{\r\n            display: 'flex',\r\n            flexDirection: 'row',\r\n            flexWrap: 'wrap',\r\n            gap: 2,\r\n          }}\r\n        >\r\n          {reportTypeOptions.map((option) => (\r\n            <Box\r\n              key={option.value}\r\n              sx={{\r\n                position: 'relative',\r\n                transition: 'all 0.2s ease-in-out',\r\n                cursor: 'pointer',\r\n                borderRadius: 2,\r\n                backgroundColor:\r\n                  settings.reportType === option.value\r\n                    ? alpha('#1976d2', 0.08)\r\n                    : 'white',\r\n                '&:hover': {\r\n                  backgroundColor:\r\n                    settings.reportType === option.value\r\n                      ? alpha('#1976d2', 0.12)\r\n                      : alpha('#1976d2', 0.04),\r\n                  transform: 'translateY(-2px)',\r\n                },\r\n              }}\r\n            >\r\n              <FormControlLabel\r\n                value={option.value}\r\n                control={\r\n                  <Radio\r\n                    sx={{\r\n                      color: 'grey.400',\r\n                      '&.Mui-checked': {\r\n                        color: '#1976d2',\r\n                      },\r\n                      '&:hover': {\r\n                        backgroundColor: 'transparent',\r\n                      },\r\n                    }}\r\n                  />\r\n                }\r\n                label={\r\n                  <Typography\r\n                    sx={{\r\n                      fontWeight: settings.reportType === option.value ? 600 : 500,\r\n                      fontSize: '0.875rem',\r\n                      color:\r\n                        settings.reportType === option.value\r\n                          ? '#1976d2'\r\n                          : 'text.primary',\r\n                      transition: 'all 0.2s ease-in-out',\r\n                    }}\r\n                  >\r\n                    {option.label}\r\n                  </Typography>\r\n                }\r\n                sx={{\r\n                  m: 0,\r\n                  px: 2,\r\n                  py: 1.5,\r\n                  width: '100%',\r\n                  '& .MuiFormControlLabel-label': {\r\n                    userSelect: 'none',\r\n                  },\r\n                }}\r\n              />\r\n            </Box>\r\n          ))}\r\n        </RadioGroup>\r\n      </Box>\r\n\r\n      {/* Conditional Rendering */}\r\n      {!isCurrentReportTypeSupported ? (\r\n        <ComingSoonFallback \r\n          reportType={settings.reportType} \r\n          reportTypeOptions={reportTypeOptions} \r\n        />\r\n      ) : (\r\n        <>\r\n          {/* Report Components Section with Collapsible Cards */}\r\n          <Box sx={{ mb: 3 }}>\r\n            <Typography\r\n              variant=\"subtitle1\"\r\n              sx={{\r\n                fontWeight: 600,\r\n                color: 'text.primary',\r\n                fontSize: '1rem',\r\n                mb: 1,\r\n              }}\r\n            >\r\n              Report Components\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{ color: 'text.secondary', mb: 3 }}\r\n            >\r\n              Select which financial metrics to include in your analysis\r\n            </Typography>\r\n\r\n            {/* Grid Layout for Collapsible Sections */}\r\n            <Box sx={{ \r\n              display: 'grid', \r\n              gridTemplateColumns: { xs: '1fr', md: '1fr 1fr 1fr' }, \r\n              gap: 3,\r\n              maxWidth: '100%',\r\n              alignItems: 'start'\r\n            }}>\r\n              {settingsSections.map((section) => (\r\n                <CollapsibleCard\r\n                  key={section.key}\r\n                  section={section}\r\n                  settings={settings}\r\n                  handleSwitchChange={handleSwitchChange}\r\n                  isExpanded={expandedCards[section.key] || false}\r\n                  onToggle={toggleCard}\r\n                />\r\n              ))}\r\n            </Box>\r\n          </Box>\r\n\r\n          {/* Custom Analysis Prompt Section */}\r\n          <Card\r\n            elevation={0}\r\n            sx={{\r\n              mb: 3,\r\n              width: 600,\r\n              transition: 'all 0.2s ease-in-out',\r\n              '&:hover': {\r\n                borderColor: '#1976d2',\r\n                backgroundColor: alpha('#1976d2', 0.02),\r\n              },\r\n            }}\r\n          >\r\n            <Box\r\n              component=\"fieldset\"\r\n              sx={{\r\n                border: '2px solid',\r\n                borderColor: 'grey.400',\r\n                borderRadius: 1,\r\n                margin: 0,\r\n                position: 'relative',\r\n                backgroundColor: 'white',\r\n                transition: 'border-color 0.2s ease-in-out',\r\n                '&:focus-within': {\r\n                  borderColor: '#1976d2',\r\n                },\r\n                '&:focus-within legend': {\r\n                  color: '#1976d2',\r\n                },\r\n              }}\r\n            >\r\n              <Box\r\n                component=\"legend\"\r\n                sx={{\r\n                  fontSize: '0.875rem',\r\n                  fontWeight: 500,\r\n                  fontFamily: '\"Roboto\",\"Helvetica\",\"Arial\",sans-serif',\r\n                  color: 'text.secondary',\r\n                  padding: '0 2px',\r\n                  marginLeft: 1,\r\n                  transition: 'color 0.2s ease-in-out',\r\n                }}\r\n              >\r\n                Prompt\r\n              </Box>\r\n              \r\n              <TextField\r\n                multiline\r\n                rows={5}\r\n                value={settings.prompt}\r\n                onChange={handlePromptChange}\r\n                placeholder=\"Sample: Summarize financial performance for the current fiscal year, covering expenses, operational efficiency, liquidity, and profit & loss (monthly, YTD, and 13-month trailing). Include a balance sheet review, cost and COGS analysis, and conclude with executive insights, key focus areas, and recommendations for 2025\"\r\n                fullWidth\r\n                variant=\"outlined\"\r\n                sx={{\r\n                  '& .MuiOutlinedInput-root': {\r\n                    '& fieldset': {\r\n                      border: 'none',\r\n                    },\r\n                    '&:hover fieldset': {\r\n                      border: 'none',\r\n                    },\r\n                    '&.Mui-focused fieldset': {\r\n                      border: 'none',\r\n                    },\r\n                  },\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: 1.6,\r\n                    '&::placeholder': {\r\n                      color: 'text.secondary',\r\n                      opacity: 0.7,\r\n                    },\r\n                  },\r\n                }}\r\n              />\r\n            </Box>\r\n          </Card>\r\n\r\n          {/* Save Button */}\r\n          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-start' }}>\r\n            <Button\r\n              variant=\"contained\"\r\n              size=\"medium\"\r\n              onClick={handleSave}\r\n              disabled={!hasChanges || isSaving}\r\n              sx={{\r\n                textTransform: 'none',\r\n                minWidth: '120px',\r\n                px: 3,\r\n              }}\r\n            >\r\n              {isSaving ? 'SAVING...' : 'SAVE'}\r\n            </Button>\r\n          </Box>\r\n        </>\r\n      )}\r\n\r\n      {/* Success Snackbar */}\r\n      <Snackbar\r\n        open={showSuccess}\r\n        autoHideDuration={4000}\r\n        onClose={handleCloseSuccess}\r\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\r\n      >\r\n        <Alert\r\n          onClose={handleCloseSuccess}\r\n          severity=\"success\"\r\n          variant=\"filled\"\r\n          sx={{\r\n            backgroundColor: '#1976d2',\r\n            '& .MuiAlert-icon': {\r\n              color: 'white',\r\n            },\r\n          }}\r\n        >\r\n          Settings saved successfully!\r\n        </Alert>\r\n      </Snackbar>\r\n\r\n      {/* Error Snackbar */}\r\n      <Snackbar\r\n        open={showError}\r\n        autoHideDuration={6000}\r\n        onClose={handleCloseError}\r\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\r\n      >\r\n        <Alert\r\n          onClose={handleCloseError}\r\n          severity=\"error\"\r\n          variant=\"filled\"\r\n        >\r\n          {errorMessage}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ReportSettings;Z"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,UAAU,EACVC,OAAO,QACF,eAAe;AAEtB,SACEC,gBAAgB,IAAIC,cAAc,EAClCC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAE5B,SACEC,8BAA8B,EAC9BC,qBAAqB,QAChB,mCAAmC;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAkB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChE,MAAMC,KAAK,GAAG1B,QAAQ,CAAC,CAAC;EAExB,MAAM2B,mBAAmB,GAAG,EAAAF,qBAAA,GAAAF,iBAAiB,CAACK,IAAI,CAChDC,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAKR,UAC7B,CAAC,cAAAG,qBAAA,uBAF2BA,qBAAA,CAEzBM,KAAK,KAAIT,UAAU;EAEtB,oBACEJ,OAAA,CAACzB,GAAG;IAACuC,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAC,QAAA,eACzBhB,OAAA,CAACtB,IAAI;MACHuC,SAAS,EAAE,CAAE;MACbH,EAAE,EAAE;QACFI,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,eAAe,EAAErC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC;QACvCsC,MAAM,EAAE,aAAatC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;QAC5CuC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,eAEFhB,OAAA,CAACzB,GAAG;QAACuC,EAAE,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAX,QAAA,gBAClFhB,OAAA,CAACN,cAAc;UAACoB,EAAE,EAAE;YAAEc,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE;UAAO;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DjC,OAAA,CAACzB,GAAG;UAAAyC,QAAA,gBACFhB,OAAA,CAACxB,UAAU;YACT0D,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFc,KAAK,EAAE,SAAS;cAChBO,UAAU,EAAE,GAAG;cACfN,QAAQ,EAAE,QAAQ;cAClBX,EAAE,EAAE;YACN,CAAE;YAAAF,QAAA,GAEDP,mBAAmB,EAAC,iBACvB;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACxB,UAAU;YACT0D,OAAO,EAAC,OAAO;YACfpB,EAAE,EAAE;cACFc,KAAK,EAAE,gBAAgB;cACvBC,QAAQ,EAAE,MAAM;cAChBO,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;YACV,CAAE;YAAArB,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;;AAED;AAAA3B,EAAA,CApDMH,kBAAkB;EAAA,QACRrB,QAAQ;AAAA;AAAAwD,EAAA,GADlBnC,kBAAkB;AAqDxB,MAAMoC,eAAe,GAAGA,CAAC;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,kBAAkB;EAAEC,UAAU;EAAEC;AAAS,CAAC,KAAK;EAC3F,MAAMC,WAAW,GAAIC,CAAC,IAAK;IACzBA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBH,QAAQ,CAACJ,OAAO,CAACQ,GAAG,CAAC;EACvB,CAAC;EAED,oBACEhD,OAAA,CAACtB,IAAI;IACHuC,SAAS,EAAE,CAAE;IACbH,EAAE,EAAE;MACFO,MAAM,EAAE,aAAatC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;MAC5CuC,YAAY,EAAE,CAAC;MACf2B,UAAU,EAAE,sBAAsB;MAClC,SAAS,EAAE;QACTC,SAAS,EAAE,aAAanE,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;QAC/CoE,WAAW,EAAEpE,KAAK,CAAC,SAAS,EAAE,GAAG;MACnC;IACF,CAAE;IAAAiC,QAAA,gBAGFhB,OAAA,CAACzB,GAAG;MACF6E,OAAO,EAAEP,WAAY;MACrB/B,EAAE,EAAE;QACFK,CAAC,EAAE,CAAC;QACJkC,MAAM,EAAE,SAAS;QACjB7B,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpB4B,cAAc,EAAE,eAAe;QAC/B,SAAS,EAAE;UACTlC,eAAe,EAAErC,KAAK,CAAC,SAAS,EAAE,IAAI;QACxC;MACF,CAAE;MAAAiC,QAAA,gBAEFhB,OAAA,CAACzB,GAAG;QAACuC,EAAE,EAAE;UAAEyC,IAAI,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBACnBhB,OAAA,CAACxB,UAAU;UACT0D,OAAO,EAAC,IAAI;UACZpB,EAAE,EAAE;YACFqB,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,cAAc;YACrBC,QAAQ,EAAE,QAAQ;YAClBX,EAAE,EAAE;UACN,CAAE;UAAAF,QAAA,EAEDwB,OAAO,CAACgB;QAAK;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACbjC,OAAA,CAACxB,UAAU;UACT0D,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFc,KAAK,EAAE,gBAAgB;YACvBC,QAAQ,EAAE;UACZ,CAAE;UAAAb,QAAA,EAEDwB,OAAO,CAACiB;QAAW;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNjC,OAAA,CAACT,UAAU;QACTmE,IAAI,EAAC,OAAO;QACZ5C,EAAE,EAAE;UACFmC,UAAU,EAAE,qBAAqB;UACjCU,SAAS,EAAEhB,UAAU,GAAG,gBAAgB,GAAG;QAC7C,CAAE;QAAA3B,QAAA,eAEFhB,OAAA,CAACJ,cAAc;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNjC,OAAA,CAACV,QAAQ;MAACsE,EAAE,EAAEjB,UAAW;MAACkB,OAAO,EAAC,MAAM;MAACC,aAAa;MAAA9C,QAAA,gBACpDhB,OAAA,CAACrB,OAAO;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXjC,OAAA,CAACzB,GAAG;QAACuC,EAAE,EAAE;UAAEK,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE,CAAC;UAAEC,MAAM,EAAE,OAAO;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAjD,QAAA,EAC1DwB,OAAO,CAAC0B,OAAO,CAACC,GAAG,CAAC,CAACxD,MAAM,EAAEyD,KAAK,kBACjCpE,OAAA,CAACzB,GAAG;UAEFuC,EAAE,EAAE;YACFU,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpB4B,cAAc,EAAE,eAAe;YAC/Be,EAAE,EAAE,GAAG;YACPC,YAAY,EAAEF,KAAK,GAAG5B,OAAO,CAAC0B,OAAO,CAACK,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM;YACvEC,iBAAiB,EAAEzF,KAAK,CAAC,SAAS,EAAE,GAAG;UACzC,CAAE;UAAAiC,QAAA,gBAEFhB,OAAA,CAACR,OAAO;YACNgE,KAAK,EAAE7C,MAAM,CAAC8D,OAAQ;YACtBC,KAAK;YACLC,SAAS,EAAC,WAAW;YACrB7D,EAAE,EAAE;cACF,uBAAuB,EAAE;gBACvBe,QAAQ,EAAE,UAAU;gBACpBO,QAAQ,EAAE,OAAO;gBACjBwC,UAAU,EAAE;cACd;YACF,CAAE;YAAA5D,QAAA,eAEFhB,OAAA,CAACzB,GAAG;cAACuC,EAAE,EAAE;gBACPyC,IAAI,EAAE,CAAC;gBACP,SAAS,EAAE;kBACTnC,eAAe,EAAErC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC;kBACvCuC,YAAY,EAAE,CAAC;kBACfuD,OAAO,EAAE,UAAU;kBACnBxC,MAAM,EAAE;gBACV;cACF,CAAE;cAAArB,QAAA,eACAhB,OAAA,CAACxB,UAAU;gBACT0D,OAAO,EAAC,OAAO;gBACfpB,EAAE,EAAE;kBACFqB,UAAU,EAAE,GAAG;kBACfP,KAAK,EAAE,cAAc;kBACrBC,QAAQ,EAAE,QAAQ;kBAClB+C,UAAU,EAAE;gBACd,CAAE;gBAAA5D,QAAA,EAEDL,MAAM,CAACE;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEVjC,OAAA,CAACvB,MAAM;YACLqG,OAAO,EAAErC,QAAQ,CAAC9B,MAAM,CAACqC,GAAG,CAAE;YAC9B+B,QAAQ,EAAErC,kBAAkB,CAAC/B,MAAM,CAACqC,GAAG,CAAE;YACzCgC,UAAU,EAAE;cAAE,YAAY,EAAE;YAAa,CAAE;YAC3ClE,EAAE,EAAE;cAAEmE,EAAE,EAAE;YAAE;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA,GAlDGtB,MAAM,CAACqC,GAAG;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEX,CAAC;AAACiD,GAAA,GAjII3C,eAAe;AAmIrB,MAAM4C,cAAc,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,GAAA;EACxC,MAAM7E,KAAK,GAAG1B,QAAQ,CAAC,CAAC;EACxB,MAAM,CAAC2D,QAAQ,EAAE6C,WAAW,CAAC,GAAGjH,QAAQ,CAAC;IACvC+B,UAAU,EAAE,WAAW;IACvBmF,aAAa,EAAE,IAAI;IACnBC,SAAS,EAAE,IAAI;IACfC,iBAAiB,EAAE,IAAI;IACvBC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE,IAAI;IACfC,mBAAmB,EAAE,IAAI;IACzBC,0BAA0B,EAAE,IAAI;IAChCC,6BAA6B,EAAE,IAAI;IACnCC,oBAAoB,EAAE,IAAI;IAC1BC,uBAAuB,EAAE,IAAI;IAC7BC,wBAAwB,EAAE,IAAI;IAC9BC,mBAAmB,EAAE,IAAI;IACzBC,kBAAkB,EAAE,IAAI;IACxBC,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,IAAI;IACtBC,qBAAqB,EAAE,IAAI;IAC3BC,OAAO,EAAE,IAAI;IACbC,GAAG,EAAE,IAAI;IACTC,YAAY,EAAE,IAAI;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxI,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyI,SAAS,EAAEC,YAAY,CAAC,GAAG1I,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC2I,WAAW,EAAEC,cAAc,CAAC,GAAG5I,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6I,SAAS,EAAEC,YAAY,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+I,YAAY,EAAEC,eAAe,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiJ,UAAU,EAAEC,aAAa,CAAC,GAAGlJ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGpJ,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAMqJ,UAAU,GAAIC,OAAO,IAAK;IAC9BF,gBAAgB,CAACG,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACD,OAAO,GAAG,CAACC,IAAI,CAACD,OAAO;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMtH,iBAAiB,GAAG,CACxB;IACEO,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMgH,gBAAgB,GAAG,CACvB;IACE7E,GAAG,EAAE,mBAAmB;IACxBQ,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,uDAAuD;IACpES,OAAO,EAAE,CACP;MACElB,GAAG,EAAE,eAAe;MACpBnC,KAAK,EAAE,gDAAgD;MACvD4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,WAAW;MAChBnC,KAAK,EAAE,mBAAmB;MAC1B4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,mBAAmB;MACxBnC,KAAK,EAAE,qBAAqB;MAC5B4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,iBAAiB;MACtBnC,KAAK,EAAE,mBAAmB;MAC1B4D,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEzB,GAAG,EAAE,gBAAgB;IACrBQ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,6DAA6D;IAC1ES,OAAO,EAAE,CACP;MACElB,GAAG,EAAE,WAAW;MAChBnC,KAAK,EAAE,6BAA6B;MACpC4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,qBAAqB;MAC1BnC,KAAK,EAAE,wBAAwB;MAC/B4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,4BAA4B;MACjCnC,KAAK,EAAE,gCAAgC;MACvC4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,+BAA+B;MACpCnC,KAAK,EAAE,oCAAoC;MAC3C4D,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEzB,GAAG,EAAE,uBAAuB;IAC5BQ,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uDAAuD;IACpES,OAAO,EAAE,CACP;MACElB,GAAG,EAAE,sBAAsB;MAC3BnC,KAAK,EAAE,8BAA8B;MACrC4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,yBAAyB;MAC9BnC,KAAK,EAAE,gCAAgC;MACvC4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,0BAA0B;MAC/BnC,KAAK,EAAE,4BAA4B;MACnC4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,qBAAqB;MAC1BnC,KAAK,EAAE,uBAAuB;MAC9B4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,oBAAoB;MACzBnC,KAAK,EAAE,sBAAsB;MAC7B4D,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEzB,GAAG,EAAE,kBAAkB;IACvBQ,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,2CAA2C;IACxDS,OAAO,EAAE,CACP;MACElB,GAAG,EAAE,iBAAiB;MACtBnC,KAAK,EAAE,oBAAoB;MAC3B4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,YAAY;MACjBnC,KAAK,EAAE,aAAa;MACpB4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,kBAAkB;MACvBnC,KAAK,EAAE,qBAAqB;MAC5B4D,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEzB,GAAG,EAAE,eAAe;IACpBQ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,kDAAkD;IAC/DS,OAAO,EAAE,CACP;MACElB,GAAG,EAAE,uBAAuB;MAC5BnC,KAAK,EAAE,mBAAmB;MAC1B4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,SAAS;MACdnC,KAAK,EAAE,SAAS;MAChB4D,OAAO,EAAE;IACX,CAAC,EACD;MACEzB,GAAG,EAAE,KAAK;MACVnC,KAAK,EAAE,KAAK;MACZ4D,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,EACD;IACEzB,GAAG,EAAE,cAAc;IACnBQ,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,sDAAsD;IACnES,OAAO,EAAE,CACP;MACElB,GAAG,EAAE,cAAc;MACnBnC,KAAK,EAAE,eAAe;MACtB4D,OAAO,EAAE;IACX,CAAC;EAEL,CAAC,CACF;EAED,MAAMqD,4BAA4B,GAAGrF,QAAQ,CAACrC,UAAU,KAAK,WAAW;;EAExE;EACA9B,SAAS,CAAC,MAAM;IACd,MAAMyJ,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAAC3C,SAAS,EAAE;MAEhB,IAAI;QACF2B,YAAY,CAAC,IAAI,CAAC;QAClB,MAAMiB,QAAQ,GAAG,MAAMnI,8BAA8B,CAACuF,SAAS,EAAE,WAAW,CAAC;QAE7E,IAAI4C,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIF,QAAQ,CAACC,IAAI,CAACA,IAAI,EAAE;UAC/C,MAAMA,IAAI,GAAGE,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI;UAE3F,IAAIA,IAAI,IAAIA,IAAI,CAACI,aAAa,EAAE;YAC9B/C,WAAW,CAACsC,IAAI,KAAK;cACnB,GAAGA,IAAI;cACP,GAAGK,IAAI,CAACI,aAAa;cACrB1B,MAAM,EAAEsB,IAAI,CAACK,iBAAiB,IAAIV,IAAI,CAACjB;YACzC,CAAC,CAAC,CAAC;UACL;QACF;MACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;QACdlB,eAAe,CAAC,yBAAyB,CAAC;QAC1CF,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,SAAS;QACRJ,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDgB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAAC3C,SAAS,CAAC,CAAC;EAEf,MAAM1C,kBAAkB,GAAI8F,KAAK,IAAMC,KAAK,IAAK;IAC/CnD,WAAW,CAACsC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACY,KAAK,GAAGC,KAAK,CAACC,MAAM,CAAC5D;IACxB,CAAC,CAAC,CAAC;IACHyC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMoB,kBAAkB,GAAIF,KAAK,IAAK;IACpCnD,WAAW,CAACsC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPjB,MAAM,EAAE8B,KAAK,CAACC,MAAM,CAAC9H;IACvB,CAAC,CAAC,CAAC;IACH2G,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMqB,sBAAsB,GAAG,MAAOH,KAAK,IAAK;IAC9C,MAAMI,aAAa,GAAGJ,KAAK,CAACC,MAAM,CAAC9H,KAAK;IACxC0E,WAAW,CAACsC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPxH,UAAU,EAAEyI;IACd,CAAC,CAAC,CAAC;IAEH,IAAIA,aAAa,KAAK,WAAW,EAAE;MACjCtB,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;IAEA,IAAI,CAACnC,SAAS,EAAE;IAEhB,IAAI;MACF2B,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMiB,QAAQ,GAAG,MAAMnI,8BAA8B,CAACuF,SAAS,EAAEyD,aAAa,CAAC;MAE/E,IAAIb,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIF,QAAQ,CAACC,IAAI,CAACA,IAAI,EAAE;QAC/C,MAAMA,IAAI,GAAGE,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI;QAE3F,IAAIA,IAAI,IAAIA,IAAI,CAACI,aAAa,EAAE;UAC9B/C,WAAW,CAACsC,IAAI,KAAK;YACnB,GAAGA,IAAI;YACP,GAAGK,IAAI,CAACI,aAAa;YACrB1B,MAAM,EAAEsB,IAAI,CAACK,iBAAiB,IAAI;UACpC,CAAC,CAAC,CAAC;QACL;MACF;MACAf,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdlB,eAAe,CAAC,yBAAyB,CAAC;MAC1CF,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,SAAS;MACRJ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM+B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC1D,SAAS,IAAI,CAAC0C,4BAA4B,EAAE;IAEjDjB,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,MAAM;QAAEzG,UAAU;QAAEuG,MAAM;QAAE,GAAG0B;MAAc,CAAC,GAAG5F,QAAQ;MAEzD,MAAMsG,OAAO,GAAG;QACdV,aAAa;QACbC,iBAAiB,EAAE3B;MACrB,CAAC;MAED,MAAM7G,qBAAqB,CAACsF,SAAS,EAAE3C,QAAQ,CAACrC,UAAU,EAAE2I,OAAO,CAAC;MAEpExB,aAAa,CAAC,KAAK,CAAC;MACpBN,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MAAA,IAAAS,eAAA,EAAAC,oBAAA;MACd5B,eAAe,CAAC,EAAA2B,eAAA,GAAAT,KAAK,CAACP,QAAQ,cAAAgB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBf,IAAI,cAAAgB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,yBAAyB,CAAC;MAC3E/B,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,SAAS;MACRN,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BlC,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMmC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjC,YAAY,CAAC,KAAK,CAAC;IACnBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,IAAIP,SAAS,EAAE;IACb,oBACE9G,OAAA,CAACzB,GAAG;MAACuC,EAAE,EAAE;QACPC,KAAK,EAAE,MAAM;QACbS,OAAO,EAAE,MAAM;QACf8B,cAAc,EAAE,QAAQ;QACxB5B,UAAU,EAAE,QAAQ;QACpB2H,SAAS,EAAE;MACb,CAAE;MAAArI,QAAA,eACAhB,OAAA,CAACd,gBAAgB;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEjC,OAAA,CAACzB,GAAG;IAACuC,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEzBhB,OAAA,CAACzB,GAAG;MAACuC,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACjBhB,OAAA,CAACxB,UAAU;QACT0D,OAAO,EAAC,WAAW;QACnBpB,EAAE,EAAE;UACFqB,UAAU,EAAE,GAAG;UACfP,KAAK,EAAE,cAAc;UACrBC,QAAQ,EAAE,MAAM;UAChBX,EAAE,EAAE;QACN,CAAE;QAAAF,QAAA,EACH;MAED;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbjC,OAAA,CAACb,UAAU;QACTyB,KAAK,EAAE6B,QAAQ,CAACrC,UAAW;QAC3B2E,QAAQ,EAAE6D,sBAAuB;QACjC9H,EAAE,EAAE;UACFU,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,KAAK;UACpB6H,QAAQ,EAAE,MAAM;UAChB3H,GAAG,EAAE;QACP,CAAE;QAAAX,QAAA,EAEDX,iBAAiB,CAAC8D,GAAG,CAAExD,MAAM,iBAC5BX,OAAA,CAACzB,GAAG;UAEFuC,EAAE,EAAE;YACFyI,QAAQ,EAAE,UAAU;YACpBtG,UAAU,EAAE,sBAAsB;YAClCI,MAAM,EAAE,SAAS;YACjB/B,YAAY,EAAE,CAAC;YACfF,eAAe,EACbqB,QAAQ,CAACrC,UAAU,KAAKO,MAAM,CAACC,KAAK,GAChC7B,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,GACtB,OAAO;YACb,SAAS,EAAE;cACTqC,eAAe,EACbqB,QAAQ,CAACrC,UAAU,KAAKO,MAAM,CAACC,KAAK,GAChC7B,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,GACtBA,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC;cAC5B4E,SAAS,EAAE;YACb;UACF,CAAE;UAAA3C,QAAA,eAEFhB,OAAA,CAACZ,gBAAgB;YACfwB,KAAK,EAAED,MAAM,CAACC,KAAM;YACpB4I,OAAO,eACLxJ,OAAA,CAACX,KAAK;cACJyB,EAAE,EAAE;gBACFc,KAAK,EAAE,UAAU;gBACjB,eAAe,EAAE;kBACfA,KAAK,EAAE;gBACT,CAAC;gBACD,SAAS,EAAE;kBACTR,eAAe,EAAE;gBACnB;cACF;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACF;YACDpB,KAAK,eACHb,OAAA,CAACxB,UAAU;cACTsC,EAAE,EAAE;gBACFqB,UAAU,EAAEM,QAAQ,CAACrC,UAAU,KAAKO,MAAM,CAACC,KAAK,GAAG,GAAG,GAAG,GAAG;gBAC5DiB,QAAQ,EAAE,UAAU;gBACpBD,KAAK,EACHa,QAAQ,CAACrC,UAAU,KAAKO,MAAM,CAACC,KAAK,GAChC,SAAS,GACT,cAAc;gBACpBqC,UAAU,EAAE;cACd,CAAE;cAAAjC,QAAA,EAEDL,MAAM,CAACE;YAAK;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACb;YACDnB,EAAE,EAAE;cACF2I,CAAC,EAAE,CAAC;cACJC,EAAE,EAAE,CAAC;cACLrF,EAAE,EAAE,GAAG;cACPtD,KAAK,EAAE,MAAM;cACb,8BAA8B,EAAE;gBAC9B4I,UAAU,EAAE;cACd;YACF;UAAE;YAAA7H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA1DGtB,MAAM,CAACC,KAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2Dd,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGL,CAAC6F,4BAA4B,gBAC5B9H,OAAA,CAACG,kBAAkB;MACjBC,UAAU,EAAEqC,QAAQ,CAACrC,UAAW;MAChCC,iBAAiB,EAAEA;IAAkB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,gBAEFjC,OAAA,CAAAE,SAAA;MAAAc,QAAA,gBAEEhB,OAAA,CAACzB,GAAG;QAACuC,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,gBACjBhB,OAAA,CAACxB,UAAU;UACT0D,OAAO,EAAC,WAAW;UACnBpB,EAAE,EAAE;YACFqB,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,cAAc;YACrBC,QAAQ,EAAE,MAAM;YAChBX,EAAE,EAAE;UACN,CAAE;UAAAF,QAAA,EACH;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjC,OAAA,CAACxB,UAAU;UACT0D,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YAAEc,KAAK,EAAE,gBAAgB;YAAEV,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EACxC;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbjC,OAAA,CAACzB,GAAG;UAACuC,EAAE,EAAE;YACPU,OAAO,EAAE,MAAM;YACfoI,mBAAmB,EAAE;cAAEC,EAAE,EAAE,KAAK;cAAEC,EAAE,EAAE;YAAc,CAAC;YACrDnI,GAAG,EAAE,CAAC;YACNS,QAAQ,EAAE,MAAM;YAChBV,UAAU,EAAE;UACd,CAAE;UAAAV,QAAA,EACC6G,gBAAgB,CAAC1D,GAAG,CAAE3B,OAAO,iBAC5BxC,OAAA,CAACuC,eAAe;YAEdC,OAAO,EAAEA,OAAQ;YACjBC,QAAQ,EAAEA,QAAS;YACnBC,kBAAkB,EAAEA,kBAAmB;YACvCC,UAAU,EAAE6E,aAAa,CAAChF,OAAO,CAACQ,GAAG,CAAC,IAAI,KAAM;YAChDJ,QAAQ,EAAE8E;UAAW,GALhBlF,OAAO,CAACQ,GAAG;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMjB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjC,OAAA,CAACtB,IAAI;QACHuC,SAAS,EAAE,CAAE;QACbH,EAAE,EAAE;UACFI,EAAE,EAAE,CAAC;UACLH,KAAK,EAAE,GAAG;UACVkC,UAAU,EAAE,sBAAsB;UAClC,SAAS,EAAE;YACTE,WAAW,EAAE,SAAS;YACtB/B,eAAe,EAAErC,KAAK,CAAC,SAAS,EAAE,IAAI;UACxC;QACF,CAAE;QAAAiC,QAAA,eAEFhB,OAAA,CAACzB,GAAG;UACFwL,SAAS,EAAC,UAAU;UACpBjJ,EAAE,EAAE;YACFO,MAAM,EAAE,WAAW;YACnB8B,WAAW,EAAE,UAAU;YACvB7B,YAAY,EAAE,CAAC;YACfe,MAAM,EAAE,CAAC;YACTkH,QAAQ,EAAE,UAAU;YACpBnI,eAAe,EAAE,OAAO;YACxB6B,UAAU,EAAE,+BAA+B;YAC3C,gBAAgB,EAAE;cAChBE,WAAW,EAAE;YACf,CAAC;YACD,uBAAuB,EAAE;cACvBvB,KAAK,EAAE;YACT;UACF,CAAE;UAAAZ,QAAA,gBAEFhB,OAAA,CAACzB,GAAG;YACFwL,SAAS,EAAC,QAAQ;YAClBjJ,EAAE,EAAE;cACFe,QAAQ,EAAE,UAAU;cACpBM,UAAU,EAAE,GAAG;cACf6H,UAAU,EAAE,yCAAyC;cACrDpI,KAAK,EAAE,gBAAgB;cACvBiD,OAAO,EAAE,OAAO;cAChBoF,UAAU,EAAE,CAAC;cACbhH,UAAU,EAAE;YACd,CAAE;YAAAjC,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENjC,OAAA,CAACpB,SAAS;YACRsL,SAAS;YACTC,IAAI,EAAE,CAAE;YACRvJ,KAAK,EAAE6B,QAAQ,CAACkE,MAAO;YACvB5B,QAAQ,EAAE4D,kBAAmB;YAC7ByB,WAAW,EAAC,iUAAiU;YAC7UC,SAAS;YACTnI,OAAO,EAAC,UAAU;YAClBpB,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1B,YAAY,EAAE;kBACZO,MAAM,EAAE;gBACV,CAAC;gBACD,kBAAkB,EAAE;kBAClBA,MAAM,EAAE;gBACV,CAAC;gBACD,wBAAwB,EAAE;kBACxBA,MAAM,EAAE;gBACV;cACF,CAAC;cACD,uBAAuB,EAAE;gBACvBQ,QAAQ,EAAE,UAAU;gBACpB+C,UAAU,EAAE,GAAG;gBACf,gBAAgB,EAAE;kBAChBhD,KAAK,EAAE,gBAAgB;kBACvB0I,OAAO,EAAE;gBACX;cACF;YACF;UAAE;YAAAxI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPjC,OAAA,CAACzB,GAAG;QAACuC,EAAE,EAAE;UAAEyJ,EAAE,EAAE,CAAC;UAAE/I,OAAO,EAAE,MAAM;UAAE8B,cAAc,EAAE;QAAa,CAAE;QAAAtC,QAAA,eAChEhB,OAAA,CAACnB,MAAM;UACLqD,OAAO,EAAC,WAAW;UACnBwB,IAAI,EAAC,QAAQ;UACbN,OAAO,EAAE0F,UAAW;UACpB0B,QAAQ,EAAE,CAAClD,UAAU,IAAIV,QAAS;UAClC9F,EAAE,EAAE;YACF2J,aAAa,EAAE,MAAM;YACrBC,QAAQ,EAAE,OAAO;YACjBhB,EAAE,EAAE;UACN,CAAE;UAAA1I,QAAA,EAED4F,QAAQ,GAAG,WAAW,GAAG;QAAM;UAAA9E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CACH,eAGDjC,OAAA,CAAChB,QAAQ;MACP2L,IAAI,EAAE3D,WAAY;MAClB4D,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE1B,kBAAmB;MAC5B2B,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAhK,QAAA,eAExDhB,OAAA,CAACf,KAAK;QACJ4L,OAAO,EAAE1B,kBAAmB;QAC5B8B,QAAQ,EAAC,SAAS;QAClB/I,OAAO,EAAC,QAAQ;QAChBpB,EAAE,EAAE;UACFM,eAAe,EAAE,SAAS;UAC1B,kBAAkB,EAAE;YAClBQ,KAAK,EAAE;UACT;QACF,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXjC,OAAA,CAAChB,QAAQ;MACP2L,IAAI,EAAEzD,SAAU;MAChB0D,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEzB,gBAAiB;MAC1B0B,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAhK,QAAA,eAExDhB,OAAA,CAACf,KAAK;QACJ4L,OAAO,EAAEzB,gBAAiB;QAC1B6B,QAAQ,EAAC,OAAO;QAChB/I,OAAO,EAAC,QAAQ;QAAAlB,QAAA,EAEfoG;MAAY;QAAAtF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACoD,GAAA,CAtnBIF,cAAc;EAAA,QACJrG,QAAQ;AAAA;AAAAoM,GAAA,GADlB/F,cAAc;AAwnBpB,eAAeA,cAAc;AAACgG,CAAC;AAAA,IAAA7I,EAAA,EAAA4C,GAAA,EAAAgG,GAAA;AAAAE,YAAA,CAAA9I,EAAA;AAAA8I,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}